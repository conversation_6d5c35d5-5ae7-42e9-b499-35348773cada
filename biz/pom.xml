<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sale</artifactId>
        <groupId>com.wsgjp.ct</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sale-biz</artifactId>
    <version>2.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>common</artifactId>
            <version>${common.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wsgjp.ct</groupId>
                    <artifactId>bill-core-handle</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct.sale</groupId>
            <artifactId>platform</artifactId>
            <version>${platfrom.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wsgjp.ct</groupId>
                    <artifactId>sale-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>sale-sdk</artifactId>
            <version>${sale.sdk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wsgjp.ct.sale</groupId>
                    <artifactId>platform</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>1.3.7</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-maven-plugin</artifactId>
            <version>1.3.7</version>
        </dependency>
        <!--eshoporder的包-->

        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>sale-bus</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wsgjp.ct</groupId>
                    <artifactId>monitor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>baseinfo-core</artifactId>
            <version>6.0.29-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wsgjp.ct</groupId>
                    <artifactId>bill-core-handle</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>redis-process-message</artifactId>
            <version>1.4.1.11-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.3party</groupId>
            <artifactId>taobao-sdk</artifactId>
            <version>20250527-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct.sis</groupId>
            <artifactId>sis-client</artifactId>
            <version>${sis.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pdd</groupId>
            <artifactId>pop-sdk</artifactId>
            <version>1.9.7402</version>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>ngp-oss</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>monitor</artifactId>
            <version>${common.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>common-enum-core</artifactId>
            <version>${common.enum.core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>wms-sdk</artifactId>
            <version>${wms.sdk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wsgjp.ct</groupId>
                    <artifactId>bill-core-handle</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>recordsheet-core</artifactId>
            <version>2.16.19-RELEASE</version>
        </dependency>
        <!--<dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>recordsheet-fbiz</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>-->
        <dependency>
            <groupId>com.wsgjp.sdj</groupId>
            <artifactId>ops-sdk</artifactId>
            <version>1.34.3-RELEASE</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.wsgjp.ct</groupId>-->
        <!--            <artifactId>stark-sdk</artifactId>-->
        <!--            <version>5.4.4-RELEASE</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>ngp-export-sdk</artifactId>
            <version>5.9-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>ngp-open-notify-sdk</artifactId>
            <version>RELEASE-5.9.1</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.6.3</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.7</version>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-open</artifactId>
            <version>4.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>common</artifactId>
            <version>${common.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wsgjp.ct</groupId>
                    <artifactId>bill-core-handle</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct.sale</groupId>
            <artifactId>platform-sdk</artifactId>
            <version>${platfrom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>bill-core-handle</artifactId>
            <version>${bill.core.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>kryo</artifactId>
            <version>5.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct.framework</groupId>
            <artifactId>framework</artifactId>
            <version>1.2-RELEASE</version>
        </dependency>
    </dependencies>
</project>