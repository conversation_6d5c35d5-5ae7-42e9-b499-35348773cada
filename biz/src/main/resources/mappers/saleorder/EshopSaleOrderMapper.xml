<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopSaleOrderMapper">
    <sql id="main_order">
        `order`.trade_order_id,
        `order`.id,
        `order`.otype_id,
        `order`.profile_id,
        `order`.create_type,
        `order`.creator_id,
        `creator`.fullname AS creatorName,
        `order`.platform_trade_state,
        `order`.local_trade_state,
        `order`.local_refund_process_state,
        `order`.seller_flag,
        `order`.platform_store_id,
        `order`.platform_store_code,
        `order`.business_type,
        `order`.order_source_type,
        `order`.deliver_type,
        `order`.customer_expected_freight_name,
        `order`.pay_time_type,
        `order`.trade_create_time,
        `order`.trade_modified_time,
        `order`.trade_finish_time,
        `order`.trade_pay_time,
        `order`.create_time,
        `order`.update_time,
        `order`.modified_time,
        `order`.platform_freight_name,
        `order`.platform_freight_code,
        `order`.platform_stock_id,
        `order`.platform_stock_code,
        `order`.buyer_trade_total,
        `order`.dised_taxed_total,
        `order`.ptype_preferential_total,
        `order`.tax_total,
        `order`.total,
        `order`.order_buyer_freight_fee,
        `order`.ptype_service_fee,
        `order`.mapping_state,
        `order`.order_deliver_required,
        `order`.buyer_id,
        `order`.btype_id,
        `order`.ktype_id,
        `order`.dtype_id,
        `order`.seller_memo,
        `order`.buyer_message,
        `order`.platform_order_preferential_total,
        `order`.remark,
        `order`.pay_no,
        `order`.process_state,
        `order`.pay_btype_id,
        `order`.commission_btype_id,
        `order`.mode_of_payment,
        `order`.platform_parent_order_id,
        `order`.platform_stock_id,
        `order`.merchant_payment_account,
        `order`.platform_stock_code,
        `order`.re_send_state,
        `order`.platform_distributor_name,
        `order`.trade_total,
        `order`.buyer_unpaid_total,
        `order`.buyer_paid_total,
        `order`.distribution_tax_total,
        `smap`.platform_store_name AS 'platform_stock_name',
        `otype`.fullname AS 'otype_name',
        `eshop`.eshop_type AS shopType,
        `etype`.fullname AS 'etype_name',
        `etype`.id AS 'etype_id',
        `ktype`.fullname AS 'ktype_name',
        `btype`.fullname AS 'btype_name',
        `order`.local_freight_name AS 'local_freight_name',
        `order`.local_freight_code,
        `order`.local_freight_bill_no,
        (`order`.dised_taxed_total + `order`.order_buyer_freight_fee + `order`.ptype_service_fee) AS customerPayment,
        `buyer`.customer_receiver AS 'eshopBuyer.customer_receiver',
        `buyer`.customer_id_card AS 'eshopBuyer.customer_id_card',
        `buyer`.customer_id_card_name AS 'eshopBuyer.customer_id_card_name',
        `buyer`.customer_receiver_phone AS 'eshopBuyer.customer_receiver_phone',
        `buyer`.customer_receiver_mobile AS 'eshopBuyer.customer_receiver_mobile',
        `buyer`.customer_receiver_zip_code AS 'eshopBuyer.customer_receiver_zip_code',
        `buyer`.customer_email AS 'eshopBuyer.customer_email',
        `buyer`.customer_receiver_country AS 'eshopBuyer.customer_receiver_country',
        `buyer`.customer_receiver_province AS 'eshopBuyer.customer_receiver_province',
        `buyer`.customer_receiver_city AS 'eshopBuyer.customer_receiver_city',
        `buyer`.customer_receiver_district AS 'eshopBuyer.customer_receiver_district',
        `buyer`.customer_receiver_address AS 'eshopBuyer.customer_receiver_address',
        `buyer`.customer_receiver_full_address AS 'eshopBuyer.customer_receiver_full_address',
        `buyer`.customer_shop_account AS 'eshopBuyer.customer_shop_account',
        `buyer`.customer_receiver_town AS 'eshopBuyer.customer_receiver_town',
        `buyer`.di AS 'eshopBuyer.di',
        `buyer`.ai AS 'eshopBuyer.ai',
        `buyer`.ri AS 'eshopBuyer.ri',
        `buyer`.mi AS 'eshopBuyer.mi',
        `buyer`.addri AS 'eshopBuyer.addri',
        `buyer`.profile_id AS 'eshopBuyer.profileId',
        `buyer`.otype_id AS 'eshopBuyer.otypeId',
        `buyer`.open_receiver_id AS 'eshopBuyer.openReceiverId',
        `buyer`.open_address_id AS 'eshopBuyer.openAddressId',
        `buyer`.open_address_id_2 AS 'eshopBuyer.openAddressId2',
        `spua`.customer_receiver AS 'extend.selfPickUpInfo.customer_receiver',
        `spua`.customer_id_card AS 'extend.selfPickUpInfo.customer_id_card',
        `spua`.customer_id_card_name AS 'extend.selfPickUpInfo.customer_id_card_name',
        `spua`.customer_receiver_phone AS 'extend.selfPickUpInfo.customer_receiver_phone',
        `spua`.customer_receiver_mobile AS 'extend.selfPickUpInfo.customer_receiver_mobile',
        `spua`.customer_receiver_zip_code AS 'extend.selfPickUpInfo.customer_receiver_zip_code',
        `spua`.customer_email AS 'extend.selfPickUpInfo.customer_email',
        `spua`.customer_receiver_country AS 'extend.selfPickUpInfo.customer_receiver_country',
        `spua`.customer_receiver_province AS 'extend.selfPickUpInfo.customer_receiver_province',
        `spua`.customer_receiver_city AS 'extend.selfPickUpInfo.customer_receiver_city',
        `spua`.customer_receiver_district AS 'extend.selfPickUpInfo.customer_receiver_district',
        `spua`.customer_receiver_address AS 'extend.selfPickUpInfo.customer_receiver_address',
        `spua`.customer_receiver_full_address AS 'extend.selfPickUpInfo.customer_receiver_full_address',
        `spua`.customer_shop_account AS 'extend.selfPickUpInfo.customer_shop_account',
        `spua`.customer_receiver_town AS 'extend.selfPickUpInfo.customer_receiver_town',
        `spua`.di AS 'extend.selfPickUpInfo.di',
        `spua`.ai AS 'extend.selfPickUpInfo.ai',
        `spua`.ri AS 'extend.selfPickUpInfo.ri',
        `spua`.mi AS 'extend.selfPickUpInfo.mi',
        `spua`.addri AS 'extend.selfPickUpInfo.addri',
        `spua`.profile_id AS 'extend.selfPickUpInfo.profileId',
        `spua`.otype_id AS 'extend.selfPickUpInfo.otypeId',
        `rbi`.customer_receiver AS 'extend.realBuyer.customer_receiver',
        `rbi`.customer_id_card AS 'extend.realBuyer.customer_id_card',
        `rbi`.customer_id_card_name AS 'extend.realBuyer.customer_id_card_name',
        `rbi`.customer_receiver_phone AS 'extend.realBuyer.customer_receiver_phone',
        `rbi`.customer_receiver_mobile AS 'extend.realBuyer.customer_receiver_mobile',
        `rbi`.customer_receiver_zip_code AS 'extend.realBuyer.customer_receiver_zip_code',
        `rbi`.customer_email AS 'extend.realBuyer.customer_email',
        `rbi`.customer_receiver_country AS 'extend.realBuyer.customer_receiver_country',
        `rbi`.customer_receiver_province AS 'extend.realBuyer.customer_receiver_province',
        `rbi`.customer_receiver_city AS 'extend.realBuyer.customer_receiver_city',
        `rbi`.customer_receiver_district AS 'extend.realBuyer.customer_receiver_district',
        `rbi`.customer_receiver_address AS 'extend.realBuyer.customer_receiver_address',
        `rbi`.customer_receiver_full_address AS 'extend.realBuyer.customer_receiver_full_address',
        `rbi`.customer_shop_account AS 'extend.realBuyer.customer_shop_account',
        `rbi`.customer_receiver_town AS 'extend.realBuyer.customer_receiver_town',
        `rbi`.di AS 'extend.realBuyer.di',
        `rbi`.ai AS 'extend.realBuyer.ai',
        `rbi`.ri AS 'extend.realBuyer.ri',
        `rbi`.mi AS 'extend.realBuyer.mi',
        `rbi`.addri AS 'extend.realBuyer.addri',
        `rbi`.profile_id AS 'extend.realBuyer.profileId',
        `rbi`.otype_id AS 'extend.realBuyer.otypeId',
        `order`.deleted,
        invoice.invoice_state AS 'invoiceInfo.invoice_state',
        invoice.invoice_required AS 'invoiceInfo.invoice_required',
        invoice.invoice_type AS 'invoiceInfo.invoice_type',
        invoice.invoice_code AS 'invoiceInfo.invoice_code',
        invoice.invoice_remark AS 'invoiceInfo.invoice_remark',
        invoice.invoice_category AS 'invoiceInfo.invoice_category',
        invoice.invoice_title AS 'invoiceInfo.invoice_title',
        invoice.invoice_company AS 'invoiceInfo.invoice_company',
        invoice.invoice_register_addr AS 'invoiceInfo.invoice_register_addr',
        invoice.invoice_register_phone AS 'invoiceInfo.invoice_register_phone',
        invoice.invoice_bank AS 'invoiceInfo.invoice_bank',
        invoice.invoice_bank_account AS 'invoiceInfo.invoice_bank_account',
        invoice.secret_id AS 'invoiceInfo.secret_id',
        `order`.local_refund_state,
        `timing`.timing_type AS 'timing.timing_type',
        `timing`.plan_send_time AS 'timing.plan_send_time',
        `timing`.send_time AS 'timing.send_time',
        `timing`.cn_service AS 'timing.cn_service',
        `timing`.system_timing AS 'timing.system_timing',
        timing.promised_collect_time AS 'timing.promised_collect_time',
        timing.promised_sign_time AS 'timing.promised_sign_time',
        timing.plan_sign_time AS 'timing.plan_sign_time',
        timing.sign_time AS 'timing.sign_time',
        timing.promised_sign_startTime AS 'timing.promised_sign_startTime',
        `extend`.export_count,
        `order`.order_sale_type,
        `order`.self_delivery_mode,
        `order`.salesman,
        `order`.summary,
        `order`.platform_ptype_preferential_total,
        `order`.order_preferential_allot_total,
        `extend`.installation_service_provider AS 'extend.installation_service_provider',
        IF(TO_DAYS(`extend`.collect_time)=TO_DAYS('1990-01-01'),'',`extend`.collect_time) AS 'extend.collectTime',
        `extend`.collect_customer AS 'extend.collect_customer',
        `extend`.gather_status AS 'extend.gather_status',
        `extend`.payment_mode AS 'extend.payment_mode',
        `extend`.platform_required AS 'extend.platform_required',
        `extend`.group_header_name AS 'extend.group_header_name',
        `extend`.buyer_unique_mark AS 'extend.buyer_unique_mark',
        `extend`.trade_summary_md5 AS 'extend.trade_summary_md5',
        `extend`.advance_total AS 'extend.advance_total',
        `extend`.pickup_code AS 'extend.pickup_code',
        `extend`.platform_dispatcher_name AS 'extend.platform_dispatcher_name',
        `extend`.platform_dispather_mobile AS 'extend.platform_dispather_mobile',
        `extend`.logistics_status AS 'extend.logistics_status',
        `extend`.confirm_status AS 'extend.confirm_status',
        `extend`.pick_up_address_id AS 'extend.pick_up_address_id',
        `extend`.real_buyer_id AS 'extend.real_buyer_id',
        `extend`.platform_qc_result AS 'extend.platform_qc_result',
        `extend`.platform_identify_result AS 'extend.platform_identify_result',
        `extend`.flow_channel AS 'extend.flow_channel',
        `extend`.mall_deduction_fee AS 'extend.mall_deduction_fee',
        `extend`.seller_flag_memo AS 'extend.seller_flag_memo',
        `extend`.group_title AS 'extend.group_title',
        `extend`.group_no AS 'extend.group_no',
        `extend`.hold_time AS 'extend.hold_time',
        `extend`.take_goods_code AS 'extend.take_goods_code',
        `extend`.platform_quality_org_id AS 'extend.platform_quality_org_id',
        `extend`.platform_quality_org_name AS 'extend.platform_quality_org_name',
        `extend`.platform_quality_warehouse_code AS 'extend.platform_quality_warehouse_code',
        `extend`.platform_quality_warehouse_name AS 'extend.platform_quality_warehouse_name',
        `extend`.is_draft AS 'extend.is_draft',
        `extend`.anchor_order_preferential_total AS 'extend.anchor_order_preferential_total',
        `extend`.platform_order_subsidy_total AS 'extend.platform_order_subsidy_total',
        `extend`.sale_period_num AS 'extend.sale_period_num',
        `extend`.current_period_num AS 'extend.current_period_num',
        `extend`.national_subsidy_total AS 'extend.national_subsidy_total',
        `extend`.gathered_total AS 'extend.gathered_total',
        `otype`.ocategory,
        `order`.ptype_commission_total,
        if(`order`.distribution_dised_taxed_total = 0,`order`.dised_taxed_total,`order`.distribution_dised_taxed_total) as distribution_dised_taxed_total,
        `supplier`.id as 'extend.supplierId',
        `supplier`.fullname as 'extend.supplierName',
        distribution.distribution_buyer_trade_id as 'distribution.distribution_buyer_trade_id',
        `order`.deliver_process_type,`order`.platform_distributor_id
    </sql>

    <sql id="main_order_new">
        `order`
        .
        trade_order_id
        ,
        `order`.id,
        `order`.otype_id,
        `order`.profile_id,
        `order`.create_type,
        `order`.creator_id,
        `order`.platform_trade_state,
        `order`.local_trade_state,
        `order`.local_refund_process_state,
        `order`.seller_flag,
        `order`.platform_store_id,
        `order`.platform_store_code,
        `order`.business_type,
        `order`.order_source_type,
        `order`.deliver_type,
        `order`.pay_time_type,
        `order`.trade_create_time,
        `order`.trade_modified_time,
        `order`.trade_finish_time,
        `order`.trade_pay_time,
        `order`.create_time,
        `order`.update_time,
        `order`.modified_time,
        `order`.platform_freight_name,
        `order`.platform_freight_code,
        `order`.platform_stock_id,
        `order`.platform_stock_code,
        `order`.customer_expected_freight_name,
        `order`.customer_expected_freight_code,
        `order`.trade_total,
        `order`.buyer_trade_total,
        `order`.buyer_unpaid_total,
        ABS(SUM(ABS(detailAss.currency_dised_taxed_total) + ABS(detailAss.currency_ptype_service_fee) )) AS  buyerPaidTotal,
        SUM(ABS(detailAss.currency_dised_taxed_total)) AS disedTaxedTotal,
        `order`.ptype_preferential_total,
        `order`.tax_total,
        `order`.total,
        SUM(ABS(detailAss.currency_ptype_service_fee)) as ptypeServiceFee,
        `order`.mapping_state,
        `order`.order_deliver_required,
        `order`.buyer_id,
        `order`.btype_id,
        `order`.ktype_id,
        `order`.dtype_id,
        `order`.seller_memo,
        `order`.buyer_message,
        `order`.remark,
        `order`.pay_no,
        `order`.process_state,
        `order`.unique_mark,
        `otype`.fullname AS 'otype_name',
        `eshop`.eshop_type AS shopType,
        `etype`.fullname AS 'etype_name',
        `etype`.id AS 'etype_id',
        `ktype`.fullname AS 'ktype_name',
        `btype`.id AS 'btypeId',
        `btype`.fullname AS 'btype_name',
        `order`.order_buyer_freight_fee,
        `order`.local_freight_name AS 'freight_name',
        `order`.local_freight_bill_no,
        (`order`.dised_taxed_total + `order`.order_buyer_freight_fee + `order`.ptype_service_fee) AS customerPayment,
        `buyer`.customer_receiver AS 'eshopBuyer.customer_receiver',
        customer_id_card AS 'eshopBuyer.customer_id_card',
        customer_id_card_name AS 'eshopBuyer.customer_id_card_name',
        customer_receiver_phone AS 'eshopBuyer.customer_receiver_phone',
        customer_receiver_mobile AS 'eshopBuyer.customer_receiver_mobile',
        customer_receiver_zip_code AS 'eshopBuyer.customer_receiver_zip_code',
        customer_email AS 'eshopBuyer.customer_email',
        customer_receiver_country AS 'eshopBuyer.customer_receiver_country',
        customer_receiver_province AS 'eshopBuyer.customer_receiver_province',
        customer_receiver_city AS 'eshopBuyer.customer_receiver_city',
        customer_receiver_district AS 'eshopBuyer.customer_receiver_district',
        customer_receiver_address AS 'eshopBuyer.customer_receiver_address',
        customer_receiver_full_address AS 'eshopBuyer.customer_receiver_full_address',
        customer_shop_account AS 'eshopBuyer.customer_shop_account',
        customer_pay_account AS 'eshopBuyer.customer_pay_account',
        customer_receiver_town AS 'eshopBuyer.customer_receiver_town',
        di AS 'eshopBuyer.di',
        `order`.deleted,
        invoice.invoice_state AS 'invoiceInfo.invoice_state',
        invoice.invoice_required AS 'invoiceInfo.invoice_required',
        invoice.invoice_type AS 'invoiceInfo.invoice_type',
        invoice.invoice_code AS 'invoiceInfo.invoice_code',
        invoice.invoice_remark AS 'invoiceInfo.invoice_remark',
        invoice.invoice_category AS 'invoiceInfo.invoice_category',
        invoice.invoice_title AS 'invoiceInfo.invoice_title',
        invoice.invoice_company AS 'invoiceInfo.invoice_company',
        invoice.invoice_register_addr AS 'invoiceInfo.invoice_register_addr',
        invoice.invoice_register_phone AS 'invoiceInfo.invoice_register_phone',
        invoice.invoice_bank AS 'invoiceInfo.invoice_bank',
        invoice.invoice_bank_account AS 'invoiceInfo.invoice_bank_account',
        `order`.local_refund_state,
        `timing`.timing_type AS 'timing.timing_type',
        DATE_FORMAT(`timing`.plan_send_time,'%Y-%m-%d') AS 'timing.plan_send_time',
        `timing`.send_time AS 'timing.send_time',
        `timing`.cn_service AS 'timing.cn_service',
        `timing`.system_timing AS 'timing.system_timing',
        `extend`.export_count,
        `order`.order_sale_type,
        `order`.self_delivery_mode,
        `order`.salesman,
        `order`.summary,
        `order`.platform_ptype_preferential_total,
        `order`.order_preferential_allot_total,
        `extend`.installation_service_provider AS 'extend.installation_service_provider',
        IF(TO_DAYS(`extend`.collect_time)=TO_DAYS('1990-01-01'),'',`extend`.collect_time) AS 'extend.collectTime',
        `extend`.collect_customer AS 'extend.collect_customer',
        `extend`.gather_status AS 'extend.gather_status',
        `extend`.payment_mode AS 'extend.payment_mode',
        `extend`.platform_required AS 'extend.platform_required',
        `extend`.group_header_name AS 'extend.group_header_name',
        `otype`.ocategory,
        `order`.ptype_commission_total,
        if(`order`.distribution_dised_taxed_total = 0,`order`.dised_taxed_total,`order`.distribution_dised_taxed_total) as distribution_dised_taxed_total,`order`.platform_distributor_id
    </sql>

    <sql id="detail">
        `id`
        ,
        `id`,
        `otype_id`,
        `profile_id`,
        `trade_order_detail_id`,
        `platform_ptype_id`,
        `platform_sku_id`,
        `platform_ptype_name`,
        `platform_properties_name`,
        `platform_ptype_xcode`,
        `platform_ptype_pic_url`,
        `platform_store_id`,
        `platform_store_code`,
        `trade_total`,
        `dised_taxedtotal`,
        `refund_total`,
        `tax_total`,
        `total`,
        `trade_price`,
        `dised_taxed_price`,
        `price`,
        `qty`,
        `sub_qty`,
        `refund_qty`,
        `ptype_id`,
        `sku_id`,
        `unit`,
        `combo_row_id`,
        `local_mapping_mark`,
        `mapping_state`,
        `order_deliver_required`,
        `seller_memo`,
        `buyer_message`,
        `ktype_id`,
        `attribute`,
        ptype_service_fee,
        stock_sync_rule_id,
        remark,
        discount,
        tax_rate
    </sql>

    <sql id="order_jointable">
        LEFT JOIN pl_eshop eshop ON `eshop`.profile_id = `order`.profile_id AND `eshop`.otype_id = `order`.otype_id
        LEFT JOIN base_otype `otype` ON `otype`.profile_id = `order`.profile_id AND `otype`.id = `order`.otype_id
        LEFT JOIN pl_buyer buyer ON `buyer`.buyer_id = `order`.buyer_id AND `buyer`.profile_id = `order`.profile_id
        LEFT JOIN base_btype btype ON btype.id = `order`.btype_id AND btype.profile_id = `order`.profile_id
        LEFT JOIN base_ktype ktype ON ktype.id = `order`.ktype_id AND ktype.profile_id = `order`.profile_id
        LEFT JOIN base_etype etype ON etype.id = `order`.etype_id AND etype.profile_id = `order`.profile_id
        LEFT JOIN base_etype creator ON creator.id = `order`.creator_id AND creator.profile_id = `order`.profile_id
        LEFT JOIN pl_eshop_sale_order_timing timing ON timing.profile_id = `order`.profile_id AND timing.eshop_order_id = `order`.id
        LEFT JOIN pl_eshop_sale_order_invoice invoice ON invoice.profile_id = `order`.profile_id AND invoice.eshop_order_id = `order`.id
        LEFT JOIN pl_eshop_sale_order_extend extend ON extend.profile_id = `order`.profile_id AND extend.eshop_order_id = `order`.id
        LEFT JOIN pl_eshop_sale_order_distribution_buyer distribution ON distribution.profile_id = `order`.profile_id AND distribution.eshop_order_id = `order`.id
        LEFT JOIN pl_buyer spua ON `spua`.buyer_id = `extend`.pick_up_address_id AND `spua`.profile_id = `extend`.profile_id
        LEFT JOIN pl_buyer rbi ON `rbi`.buyer_id = `extend`.real_buyer_id AND `rbi`.profile_id = `extend`.profile_id
        LEFT JOIN base_btype supplier ON supplier.id = extend.supplier_id AND supplier.profile_id = extend.profile_id
        LEFT JOIN pl_eshop_platform_store_mapping smap ON smap.platform_store_stock_id = `order`.platform_stock_id AND smap.profile_id = `order`.profile_id  AND smap.eshop_id = `order`.otype_id
    </sql>

    <sql id="order_jointable_new">
        LEFT JOIN pl_eshop eshop ON `eshop`.profile_id = `order`.profile_id AND `eshop`.otype_id = `order`.otype_id
        LEFT JOIN base_otype `otype` ON `otype`.profile_id = `order`.profile_id AND `otype`.id = `order`.otype_id
        LEFT JOIN pl_buyer buyer ON `buyer`.buyer_id = `order`.buyer_id AND `buyer`.profile_id = `order`.profile_id
        LEFT JOIN base_btype btype ON btype.id = `order`.btype_id AND btype.profile_id = `order`.profile_id
        LEFT JOIN base_ktype ktype ON ktype.id = `order`.ktype_id AND ktype.profile_id = `order`.profile_id
        LEFT JOIN base_etype etype ON etype.id = `order`.etype_id AND etype.profile_id = `order`.profile_id
        LEFT JOIN pl_eshop_sale_order_timing timing ON timing.profile_id = `order`.profile_id AND timing.eshop_order_id = `order`.id
        LEFT JOIN pl_eshop_sale_order_invoice invoice ON invoice.profile_id = `order`.profile_id AND invoice.eshop_order_id = `order`.id
        LEFT JOIN pl_eshop_sale_order_extend extend ON extend.profile_id = `order`.profile_id AND extend.eshop_order_id = `order`.id
        INNER JOIN `acc_bill_detail_deliver` deliver ON `order`.trade_order_id = deliver.trade_order_id AND order.profile_id = deliver.profile_id and deliver.deleted = 0
        LEFT JOIN acc_bill_detail_deliver_expand deliverDetailExpand ON deliver.detail_id = deliverDetailExpand.detail_id AND deliver.profile_id = deliverDetailExpand.profile_id
        INNER JOIN `acc_bill_detail_assinfo_sale` detailAss ON detailAss.detail_id = deliver.detail_id AND detailAss.profile_id = deliver.profile_id
        INNER JOIN `acc_bill_core` bill ON deliver.profile_id = order.profile_id AND deliver.vchcode = bill.vchcode
    </sql>

    <sql id="common-query-mark">
        <choose>
            <when test="markCondition==0">
                IN
                ( SELECT order_id FROM `pl_eshop_order_mark` AS mark
                WHERE mark.order_id = `order`.id AND mark.profile_id=`order`.profile_id
                AND mark.`mark_code` IN
                <foreach collection="marks" close=")" open="(" separator="," item="mark">
                    #{mark}
                </foreach>
                )
            </when>
            <when test="markCondition==1">
                NOT IN
                (SELECT order_id FROM
                `pl_eshop_order_mark` AS mark
                WHERE mark.profile_id=`order`.profile_id AND mark.order_id = `order`.id
                and mark.mark_code in
                <foreach collection="marks" close=")" open="(" separator="," item="item">
                    #{item}
                </foreach>
                )
            </when>
            <when test="markCondition==2">
                IN
                (SELECT order_id FROM
                pl_eshop_order_mark AS mark
                WHERE mark.profile_id=`order`.profile_id AND mark.order_id = `order`.id
                and mark.mark_code in
                <foreach collection="marks" close=")" open="(" separator="," item="item">
                    #{item}
                </foreach>
                group by mark.order_id
                HAVING COUNT(DISTINCT mark.mark_code) = #{markCount})
                and
                (EXISTS
                (SELECT 1 FROM pl_eshop_order_mark AS mark
                WHERE mark.profile_id=`order`.profile_id AND mark.order_id = `order`.id
                HAVING COUNT(DISTINCT mark.mark_code) = #{markCount})
                )
            </when>
        </choose>
    </sql>

    <sql id="order_quick_filter_key_type">
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@FREIGHT_NO and keyArrays!=null and keyArrays.size()>0">
            and local_freight_bill_no in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@Trade_ID and keyArrays!=null and keyArrays.size()>0">
            and trade_order_id in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@PICK_UP_CODE and keyArrays!=null and keyArrays.size()>0">
            and `extend`.pickup_code in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@PLATFORMPARENTORDERID and keyArrays!=null and keyArrays.size()>0">
            and platform_parent_order_id in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@RECVER_MOBILE and keyArrays!=null and keyArrays.size()>0">
            and (
            buyer.mi in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            <if test="sisTradeOrderIds!=null and sisTradeOrderIds.size()>0">
                or trade_order_id in
                <foreach collection="sisTradeOrderIds" close=")" open="(" separator="," item="sisKey">
                    #{sisKey}
                </foreach>
            </if>
            )
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@RECVER_PHONE and keyArrays!=null and keyArrays.size()>0">
            and (
            buyer.pi in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            <if test="sisTradeOrderIds!=null and sisTradeOrderIds.size()>0">
                or trade_order_id in
                <foreach collection="sisTradeOrderIds" close=")" open="(" separator="," item="sisKey">
                    #{sisKey}
                </foreach>
            </if>
            )
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@SHOP_ACCOUNT and keyArrays!=null and keyArrays.size()>0">
            and (
            buyer.ai in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            <if test="sisTradeOrderIds!=null and sisTradeOrderIds.size()>0">
                or trade_order_id in
                <foreach collection="sisTradeOrderIds" close=")" open="(" separator="," item="sisKey">
                    #{sisKey}
                </foreach>
            </if>
            )
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@RECEIVER_NAME and keyArrays!=null and keyArrays.size()>0">
            and (
            buyer.ri in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            <if test="sisTradeOrderIds!=null and sisTradeOrderIds.size()>0">
                or trade_order_id in
                <foreach collection="sisTradeOrderIds" close=")" open="(" separator="," item="sisKey">
                    #{sisKey}
                </foreach>
            </if>
            )
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@BIC_CODE and keyArrays!=null and keyArrays.size()>0">
            AND (
            EXISTS(select 1 from pl_eshop_order_mark mark
            left join mark_data md on md.profile_id = mark.profile_id and md.id = mark.mark_data_id
            where mark.profile_id = #{profileId} and mark.order_id = `order`.id and
            <foreach collection="keyArrays" close=")" open="(" separator="or" item="key">
                md.big_data like concat ('%',#{key},'%')
            </foreach>)
            OR
            EXISTS(select 1 from pl_eshop_sale_order_detail
            where profile_id = #{profileId} and eshop_order_id = `order`.id and
            <foreach collection="keyArrays" close=")" open="(" separator="or" item="key">
                verify_code like concat ('%',#{key},'%')
            </foreach>)
            )
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@DISTRIBUTION_BUYER_TRADE_ID and keyArrays!=null and keyArrays.size()>0">
            AND (
            EXISTS(select 1 from pl_eshop_sale_order_distribution_buyer mark
            where profile_id = #{profileId} and eshop_order_id = `order`.id and
            <foreach collection="keyArrays" close=")" open="(" separator="or" item="key">
                distribution_buyer_trade_id like concat ('%',#{key},'%')
            </foreach>)
            OR
            EXISTS(select 1 from pl_eshop_sale_order_detail_distribution_buyer
            where profile_id = #{profileId} and eshop_order_id = `order`.id and
            <foreach collection="keyArrays" close=")" open="(" separator="or" item="key">
                distribution_buyer_trade_detail_id like concat ('%',#{key},'%')
            </foreach>)
            )
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@PLATFORM_PRODUCT_NAME and keyArrays!=null and keyArrays.size()>0">
            AND EXISTS(SELECT 1 FROM `pl_eshop_sale_order_detail` AS esd
            WHERE esd.eshop_order_id = order.id and esd.profile_id=#{profileId} AND esd.platform_ptype_name like
            concat('%',#{keyArrays[0]},'%'))
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@PLATFORM_XCODE and keyArrays!=null and keyArrays.size()>0">
            AND EXISTS(SELECT 1 FROM `pl_eshop_sale_order_detail` AS esd
            WHERE esd.eshop_order_id = order.id and esd.profile_id=#{profileId} AND esd.platform_ptype_xcode in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>)
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@PLATFORM_PROPERTIES_NAME and keyArrays!=null and keyArrays.size()>0">
            AND EXISTS(SELECT 1 FROM `pl_eshop_sale_order_detail` AS esd
            WHERE esd.eshop_order_id = order.id and esd.profile_id=#{profileId} AND esd.platform_properties_name like
            CONCAT('%',#{keyArrays[0]},'%'))
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@TAKE_GOOD_CODE and keyArrays!=null and keyArrays.size()>0">
            AND extend.take_goods_code like CONCAT('%',#{keyArrays[0]},'%')
        </if>
    </sql>

    <sql id="order_quick_filter_key_type_new">
        <if test="filterKeyTypeNew==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyTypeNew@Trade_ID and keyArrays!=null and keyArrays.size()>0">
            and (
            trade_order_id in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            OR
            platform_parent_order_id in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            OR
            distribution.distribution_buyer_trade_id in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            )
        </if>
        <if test="filterKeyTypeNew==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyTypeNew@RECVER_MOBILE and keyArrays!=null and keyArrays.size()>0">
            and (
            buyer.mi in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            <if test="sisTradeOrderIds!=null and sisTradeOrderIds.size()>0">
                or trade_order_id in
                <foreach collection="sisTradeOrderIds" close=")" open="(" separator="," item="sisKey">
                    #{sisKey}
                </foreach>
            </if>
            )
        </if>
        <if test="filterKeyTypeNew==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyTypeNew@PLATFORM_PTYPE and keyArrays!=null and keyArrays.size()>0">
            AND EXISTS(
            SELECT 1 FROM `pl_eshop_sale_order_detail` AS esd
            WHERE esd.eshop_order_id = order.id and esd.profile_id=#{profileId}
            AND (
            esd.platform_ptype_name like concat('%',#{keyArrays[0]},'%')
            OR
            esd.platform_properties_name like concat('%',#{keyArrays[0]},'%')
            OR
            esd.platform_ptype_xcode like concat('%',#{keyArrays[0]},'%')
            )
            )
        </if>
        <if test="filterKeyTypeNew==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyTypeNew@BASE_PTYPE and keyArrays!=null and keyArrays.size()>0">
            AND (EXISTS
            (
            SELECT 1 FROM `pl_eshop_sale_order_detail`  esd
            LEFT JOIN base_ptype_xcode bpx ON esd.profile_id=bpx.profile_id AND esd.ptype_id=bpx.ptype_id AND esd.sku_id=bpx.sku_id AND esd.unit=bpx.unit_id AND bpx.info_type=0
            LEFT JOIN base_ptype bp ON esd.profile_id=bp.profile_id AND esd.ptype_id=bp.ptype_id
            WHERE esd.eshop_order_id = order.id and esd.profile_id=#{profileId}
            AND (
            bp.usercode like concat('%',#{keyArrays[0]},'%')
            OR
            bp.fullname like concat('%',#{keyArrays[0]},'%')
            OR
            bp.shortname like concat('%',#{keyArrays[0]},'%')
            OR
            bpx.xcode like concat('%',#{keyArrays[0]},'%')
            )
            )
            OR
            (
            SELECT 1 FROM `pl_eshop_sale_order_detail_combo`  esd
            LEFT JOIN base_ptype_xcode bpx ON esd.profile_id=bpx.profile_id AND esd.combo_id=bpx.ptype_id  AND bpx.info_type=1
            LEFT JOIN base_ptype bp ON esd.profile_id=bp.profile_id AND esd.combo_id=bp.ptype_id
            WHERE esd.eshop_order_id = order.id and esd.profile_id=#{profileId}
            AND (
            bp.usercode like concat('%',#{keyArrays[0]},'%')
            OR
            bp.fullname like concat('%',#{keyArrays[0]},'%')
            OR
            bp.shortname like concat('%',#{keyArrays[0]},'%')
            OR
            bpx.xcode like concat('%',#{keyArrays[0]},'%')
            )
            )
            )
        </if>
        <if test="filterKeyTypeNew==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyTypeNew@FREIGHT_NO and keyArrays!=null and keyArrays.size()>0">
            and local_freight_bill_no in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="filterKeyTypeNew==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyTypeNew@PICK_UP_CODE and keyArrays!=null and keyArrays.size()>0">
            and `extend`.pickup_code in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="filterKeyTypeNew==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyTypeNew@BIC_CODE and keyArrays!=null and keyArrays.size()>0">
            AND (
            EXISTS(select 1 from pl_eshop_order_mark mark
            left join mark_data md on md.profile_id = mark.profile_id and md.id = mark.mark_data_id
            where mark.profile_id = #{profileId} and mark.order_id = `order`.id and
            <foreach collection="keyArrays" close=")" open="(" separator="or" item="key">
                md.big_data like concat ('%',#{key},'%')
            </foreach>)
            OR
            EXISTS(select 1 from pl_eshop_sale_order_detail
            where profile_id = #{profileId} and eshop_order_id = `order`.id and
            <foreach collection="keyArrays" close=")" open="(" separator="or" item="key">
                verify_code like concat ('%',#{key},'%')
            </foreach>)
            )
        </if>
        <if test="filterKeyTypeNew==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyTypeNew@TAKE_GOOD_CODE and keyArrays!=null and keyArrays.size()>0">
            AND extend.take_goods_code like CONCAT('%',#{keyArrays[0]},'%')
        </if>
        <if test="filterKeyTypeNew==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyTypeNew@SHOP_ACCOUNT and keyArrays!=null and keyArrays.size()>0">
            and (
            buyer.ai in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            <if test="sisTradeOrderIds!=null and sisTradeOrderIds.size()>0">
                or trade_order_id in
                <foreach collection="sisTradeOrderIds" close=")" open="(" separator="," item="sisKey">
                    #{sisKey}
                </foreach>
            </if>
            )
        </if>
        <if test="filterKeyTypeNew==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyTypeNew@RECEIVER_NAME and keyArrays!=null and keyArrays.size()>0">
            and (
            buyer.ri in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            <if test="sisTradeOrderIds!=null and sisTradeOrderIds.size()>0">
                or trade_order_id in
                <foreach collection="sisTradeOrderIds" close=")" open="(" separator="," item="sisKey">
                    #{sisKey}
                </foreach>
            </if>
            )
        </if>
    </sql>

    <sql id="order_query_new">
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `order`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="createType !=null and createType.size() > 0 ">
            and `order`.create_type in
            <foreach collection="createType" close=")" open="(" separator="," item="createTypes">
                #{createTypes}
            </foreach>
        </if>
        <if test="businessType!=null and businessType.size()>0">
            AND business_type NOT IN
            <foreach collection="businessType" close=")" open="(" separator="," item="type">
                #{type}
            </foreach>
        </if>
        <if test="tradeStatus !=null and tradeStatus.size() > 0 and (deliverSendState ==null or deliverSendState!=@com.wsgjp.ct.sale.biz.eshoporder.api.enums.ProcessStatusEnum@SENDED) ">
            and `order`.local_trade_state in
            <foreach collection="tradeStatus" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
        </if>
        <if test="tradeStatus !=null and tradeStatus.size() > 0 and deliverSendState !=null and  deliverSendState==@com.wsgjp.ct.sale.biz.eshoporder.api.enums.ProcessStatusEnum@SENDED">
            AND (`order`.local_trade_state in
            <foreach collection="tradeStatus" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
            OR (`order`.local_trade_state in (3,4,5,6)
            or EXISTS(SELECT 1 FROM td_bill_detail_deliver tdb
            LEFT JOIN td_bill_core tdc ON tdb.profile_id=tdc.profile_id AND tdb.vchcode=tdc.vchcode
            WHERE tdb.profile_id=`order`.profile_id AND tdb.trade_order_id=`order`.trade_order_id AND
            tdc.post_state>=600 AND `order`.otype_id = tdc.otype_id)
            or EXISTS(SELECT 1 FROM acc_bill_detail_deliver adb
            LEFT JOIN acc_bill_core adc ON adb.profile_id=adc.profile_id AND adb.vchcode=adc.vchcode
            WHERE adb.profile_id=`order`.profile_id AND adb.trade_order_id=`order`.trade_order_id AND
            adc.post_state>=600 AND `order`.otype_id = adc.otype_id))
            )
        </if>
        <if test="refundStates !=null and refundStates.size() > 0 ">
            and `order`.local_refund_state in
            <foreach collection="refundStates" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
        </if>
        <if test="deleteState !=null and deleteState.size() > 0 ">
            and `order`.deleted in
            <foreach collection="deleteState" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="exportState !=null and exportState.size() > 0 ">
            <if test="exportState.size() ==1 and exportState[0]==0 ">
                and extend.export_count = 0
            </if>
            <if test="exportState.size() ==1 and exportState[0]==1 ">
                and extend.export_count > 0
            </if>
        </if>
        <if test="ptypeType==0">
            <if test="skuIds !=null and skuIds.size()>0">
                and (
                EXISTS (SELECT 1 FROM `pl_eshop_sale_order_detail` WHERE profile_id=`order`.profile_id AND
                eshop_order_id=`order`.id AND sku_id in
                <foreach collection="skuIds" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>)
                OR (EXISTS (SELECT 1 FROM `pl_eshop_sale_order_detail_combo` WHERE profile_id=`order`.profile_id AND
                eshop_order_id=`order`.id AND combo_id in
                <foreach collection="skuIds" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>))
                )
            </if>
        </if>
        <if test="ptypeType==1">
            <if test="ptypeLabelIds !=null and ptypeLabelIds.size()>0">
                and (
                EXISTS (SELECT 1 FROM `pl_eshop_sale_order_detail` AS pesod
                left join cf_data_label_ptype label on label.resource_id=pesod.ptype_id and
                label.profile_id=pesod.profile_id
                WHERE pesod.profile_id=#{profileId} and pesod.`eshop_order_id` = `order`.id and
                label.labelfield_value_id in
                <foreach collection="ptypeLabelIds" close=")" open="(" separator="," item="labelId">
                    #{labelId}
                </foreach>)
                OR
                EXISTS (SELECT 1 FROM `pl_eshop_sale_order_detail_combo` AS combo
                left join cf_data_label_ptype label on label.resource_id=combo.combo_id and
                label.profile_id=combo.profile_id
                WHERE combo.profile_id=#{profileId} and combo.`eshop_order_id` = `order`.id and
                label.labelfield_value_id in
                <foreach collection="ptypeLabelIds" close=")" open="(" separator="," item="labelId">
                    #{labelId}
                </foreach>)
                )
            </if>
        </if>
        <if test="ptypeType==2">
            <if test="ptypeClassIds !=null and ptypeClassIds.size()>0">
                and (
                EXISTS (SELECT 1 FROM `pl_eshop_sale_order_detail` AS pesod
                left join base_ptype ptype on ptype.id=pesod.ptype_id and ptype.profile_id=pesod.profile_id
                WHERE pesod.profile_id=#{profileId} and pesod.`eshop_order_id` = `order`.id and ptype.partypeid in
                <foreach collection="ptypeClassIds" close=")" open="(" separator="," item="classId">
                    #{classId}
                </foreach>)
                or
                EXISTS (SELECT 1 FROM `pl_eshop_sale_order_detail_combo` AS combo
                left join base_ptype ptype on ptype.id=combo.combo_id and ptype.profile_id=combo.profile_id
                WHERE combo.profile_id=#{profileId} and combo.`eshop_order_id` = `order`.id and ptype.partypeid in
                <foreach collection="ptypeClassIds" close=")" open="(" separator="," item="classId">
                    #{classId}
                </foreach>)
                )
            </if>
        </if>
        <if test="freightName !=null and freightName!=''">
            and `order`.local_freight_name LIKE CONCAT('%',#{freightName},'%')
        </if>
        <if test="freightBillNo !=null and freightBillNo!=''">
            and `order`.local_freight_bill_no =#{freightBillNo}
        </if>
        <if test="flagConditionForModify==0">
            <if test="marks !=null and marks.size()>0">
                AND `order`.id
                <include refid="common-query-mark"/>
            </if>
            <if test="intSellerFlags !=null and intSellerFlags.size() > 0 ">
                and `order`.seller_flag in
                <foreach collection="intSellerFlags" close=")" open="(" separator="," item="intSellerFlag">
                    #{intSellerFlag}
                </foreach>
            </if>
        </if>
        <if test="flagConditionForModify==1">
            <if test="marks !=null and marks.size()>0 and (intSellerFlags ==null or intSellerFlags.size() == 0) ">
                AND `order`.id
                <include refid="common-query-mark"/>
            </if>
            <if test="intSellerFlags !=null and intSellerFlags.size() > 0 and( marks ==null or  marks.size()==0)">
                AND `order`.seller_flag in
                <foreach collection="intSellerFlags" close=")" open="(" separator="," item="intSellerFlag">
                    #{intSellerFlag}
                </foreach>
            </if>
            <if test="intSellerFlags !=null and intSellerFlags.size() > 0  and marks !=null and marks.size()>0">
                AND (
                `order`.id
                <include refid="common-query-mark"/>
                OR
                `order`.seller_flag in
                <foreach collection="intSellerFlags" close=")" open="(" separator="," item="intSellerFlag">
                    #{intSellerFlag}
                </foreach>
                )
            </if>
        </if>
        <if test="buyerMessageCondition==1">
            AND `order`.buyer_message=''
        </if>
        <if test="buyerMessageCondition==2">
            AND `order`.buyer_message LIKE CONCAT('%',#{buyerMessage},'%')
        </if>
        <if test="buyerMessageCondition==3 and buyerMessage!=null and buyerMessage!=''">
            AND `order`.buyer_message NOT LIKE CONCAT('%',#{buyerMessage},'%')
        </if>
        <if test="sellerMemoCondition==1">
            AND `order`.seller_memo=''
        </if>
        <if test="sellerMemoCondition==2">
            AND `order`.seller_memo LIKE CONCAT('%',#{sellerMemo},'%')
        </if>
        <if test="sellerMemoCondition==3 and sellerMemo!=null and sellerMemo!=''">
            AND `order`.seller_memo NOT LIKE CONCAT('%',#{sellerMemo},'%')
        </if>
        <if test="memoCondition==1">
            AND `order`.remark=''
        </if>
        <if test="memoCondition==2">
            AND `order`.remark LIKE CONCAT('%',#{memo},'%')
        </if>
        <if test="memoCondition==3 and memo!=null and memo!=''">
            AND `order`.remark NOT LIKE CONCAT('%',#{memo},'%')
        </if>
        <if test="platformQualityOrgName!=null and platformQualityOrgName!=''">
            AND `extend`.platform_quality_org_name LIKE CONCAT('%',#{platformQualityOrgName},'%')
        </if>
        <if test="gatherStatus !=null and gatherStatus.size() > 0 ">
            and `extend`.gather_status in
            <foreach collection="gatherStatus" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <!--    <select id="querySaleOrderList_COUNT" resultType="java.lang.Integer">
            select 1
        </select>-->

    <select id="querySaleOrderCount" resultType="java.lang.Integer">
        select count(1) from pl_eshop_sale_order `order`
        <include refid="order_jointable"/>
        WHERE `order`.profile_id=#{profileId}
        <if test="eshopOrderIds !=null and eshopOrderIds.size()>0">
            AND `order`.id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="eshopOrderId !='' and eshopOrderId !=null">
            AND `order`.id=#{id}
        </if>
        <if test="btypeId!=null and btypeId>0">
            AND `order`.btype_id=#{btypeId}
        </if>
        <if test="businessTypes!=null and businessTypes.size()>0">
            AND `order`.business_type IN
            <foreach collection="businessTypes" close=")" open="(" separator="," item="businessType">
                #{businessType}
            </foreach>
        </if>
        <if test="allowOtypeIds!=null and allowOtypeIds.size()>0 and otypeLimited">
            and `order`.otype_id in
            <foreach collection="allowOtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowKtypeIds!=null and allowKtypeIds.size()>0 and ktypeLimited">
            and `order`.Ktype_id in
            <foreach collection="allowKtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowEtypeIds!=null and allowEtypeIds.size()>0 and etypeLimited">
            and `order`.etype_id in
            <foreach collection="allowEtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
        <if test="customerAreas !=null and customerAreas.size() > 0 ">
            and
            <foreach collection="customerAreas" close=")" open="(" separator="or" item="area">
                (`buyer`.customer_receiver_province=#{area.customerReceiverProvince}
                <if test="area.customerReceiverCity != null and area.customerReceiverCity != ''">
                    and `buyer`.customer_receiver_city=#{area.customerReceiverCity}
                </if>
                <if test="area.customerReceiverDistrict != null and area.customerReceiverDistrict != ''">
                    and `buyer`.customer_receiver_district=#{area.customerReceiverDistrict}
                </if>
                )
            </foreach>
        </if>

        <include refid="order_quick_filter_key_type"/>
        <include refid="order_quick_filter_key_type_new"/>

        <include refid="order_query_new"/>

        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@TRADE_CREATE_TIME">
            AND `order`.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@PAY_TIME">
            AND `order`.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@DOWNLOAD_TIME">
            AND `order`.create_time between #{beginTime} and #{endTime}
        </if>
        <if test="intSellerFlag !=null and intSellerFlag>0">
            AND `order`.seller_flag=#{intSellerFlag}
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND `order`.mapping_state=1
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND `order`.mapping_state=0
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@Submit">
            AND `order`.process_state=1 AND `order`.mapping_state=1
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@NoSubmit">
            AND `order`.process_state=0
            AND `order`.mapping_state=1 AND `order`.order_deliver_required=1
            AND `order`.local_trade_state!=5 AND `order`.order_deliver_required=1
        </if>
        <if test="filterParameter != null">
            <include refid="order_filter"/>
        </if>
        <if test="btypeIds!=null and btypeIds.size() > 0">
            AND `order`.btype_id IN
            <foreach collection="btypeIds" close=")" open="(" separator="," item="btypeId">
                #{btypeId}
            </foreach>
        </if>
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            AND `order`.id IN
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="deleted != null ">
            AND `order`.deleted= #{deleted}
        </if>
        <if test="queryCyclePurchaseMainOrder!=null and queryCyclePurchaseMainOrder">
            AND (`order`.order_sale_type != 3 OR (`order`.order_sale_type=3 AND `order`.platform_parent_order_id=''))
        </if>
        <if test="quickFilterTypes !=null and quickFilterTypes.size() > 0 ">
            AND ( 1!=1
            <foreach collection="quickFilterTypes" close="" open="" separator="" item="quickFilterType">
                <include refid="order_quick_filter_or"/>
            </foreach>
            )
        </if>
        <if test="filterParameter != null">
            <include refid="order_quick_filter"/>
        </if>
    </select>

    <select id="querySaleOrderIds" resultType="java.math.BigInteger">
        select `order`.id from pl_eshop_sale_order `order`
        <include refid="order_jointable"/>
        WHERE `order`.profile_id=#{profileId}
        <if test="eshopOrderIds !=null and eshopOrderIds.size()>0">
            AND `order`.id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="eshopOrderId !='' and eshopOrderId !=null">
            AND `order`.id=#{id}
        </if>
        <if test="btypeId!=null and btypeId>0">
            AND `order`.btype_id=#{btypeId}
        </if>
        <if test="allowOtypeIds!=null and allowOtypeIds.size()>0 and otypeLimited">
            and `order`.otype_id in
            <foreach collection="allowOtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowKtypeIds!=null and allowKtypeIds.size()>0 and ktypeLimited">
            and `order`.Ktype_id in
            <foreach collection="allowKtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowEtypeIds!=null and allowEtypeIds.size()>0 and etypeLimited">
            and `order`.etype_id in
            <foreach collection="allowEtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
        <if test="customerAreas !=null and customerAreas.size() > 0 ">
            and
            <foreach collection="customerAreas" close=")" open="(" separator="or" item="area">
                (`buyer`.customer_receiver_province=#{area.customerReceiverProvince}
                <if test="area.customerReceiverCity != null and area.customerReceiverCity != ''">
                    and `buyer`.customer_receiver_city=#{area.customerReceiverCity}
                </if>
                <if test="area.customerReceiverDistrict != null and area.customerReceiverDistrict != ''">
                    and `buyer`.customer_receiver_district=#{area.customerReceiverDistrict}
                </if>
                )
            </foreach>
        </if>

        <include refid="order_quick_filter_key_type"/>
        <include refid="order_quick_filter_key_type_new"/>

        <include refid="order_query_new"/>

        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@TRADE_CREATE_TIME">
            AND `order`.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@PAY_TIME">
            AND `order`.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@DOWNLOAD_TIME">
            AND `order`.create_time between #{beginTime} and #{endTime}
        </if>
        <if test="intSellerFlag !=null and intSellerFlag>0">
            AND `order`.seller_flag=#{intSellerFlag}
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND `order`.mapping_state=1
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND `order`.mapping_state=0
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@Submit">
            AND `order`.process_state=1 AND `order`.mapping_state=1
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@NoSubmit">
            AND `order`.process_state=0
            AND `order`.mapping_state=1 AND `order`.order_deliver_required=1
            AND `order`.local_trade_state!=5 AND `order`.order_deliver_required=1
        </if>
        <if test="filterParameter != null">
            <include refid="order_filter"/>
        </if>
        <if test="btypeIds!=null and btypeIds.size() > 0">
            AND `order`.btype_id IN
            <foreach collection="btypeIds" close=")" open="(" separator="," item="btypeId">
                #{btypeId}
            </foreach>
        </if>
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            AND `order`.id IN
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="deleted != null ">
            AND `order`.deleted= #{deleted}
        </if>
        <if test="quickFilterTypes !=null and quickFilterTypes.size() > 0 ">
            AND ( 1!=1
            <foreach collection="quickFilterTypes" close="" open="" separator="" item="quickFilterType">
                <include refid="order_quick_filter_or"/>
            </foreach>
            )
        </if>
        <if test="filterParameter != null">
            <include refid="order_quick_filter"/>
        </if>
    </select>

    <select id="querySaleOrderList"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryOrderParameter"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        SELECT
        <include refid="main_order"/>
        FROM ${tableName}
        <include refid="order_jointable"/>
        WHERE `order`.profile_id=#{profileId}
        <if test="eshopOrderIds !=null and eshopOrderIds.size()>0">
            AND `order`.id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="selfDeliveryMode!=null and selfDeliveryMode>0">
            and `order`.self_delivery_mode=#{selfDeliveryMode}
        </if>
        <if test="businessTypes!=null and businessTypes.size()>0">
            AND `order`.business_type IN
            <foreach collection="businessTypes" close=")" open="(" separator="," item="businessType">
                #{businessType}
            </foreach>
        </if>
        <if test="eshopOrderId !='' and eshopOrderId !=null">
            AND `order`.id=#{id}
        </if>
        <if test="btypeId!=null and btypeId>0">
            AND `order`.btype_id=#{btypeId}
        </if>
        <if test="allowOtypeIds!=null and allowOtypeIds.size()>0 and otypeLimited">
            and `order`.otype_id in
            <foreach collection="allowOtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowKtypeIds!=null and allowKtypeIds.size()>0 and ktypeLimited">
            and `order`.Ktype_id in
            <foreach collection="allowKtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowEtypeIds!=null and allowEtypeIds.size()>0 and etypeLimited">
            and `order`.etype_id in
            <foreach collection="allowEtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
        <if test="customerAreas !=null and customerAreas.size() > 0 ">
            and
            <foreach collection="customerAreas" close=")" open="(" separator="or" item="area">
                (`buyer`.customer_receiver_province=#{area.customerReceiverProvince}
                <if test="area.customerReceiverCity != null and area.customerReceiverCity != ''">
                    and `buyer`.customer_receiver_city=#{area.customerReceiverCity}
                </if>
                <if test="area.customerReceiverDistrict != null and area.customerReceiverDistrict != ''">
                    and `buyer`.customer_receiver_district=#{area.customerReceiverDistrict}
                </if>
                )
            </foreach>
        </if>

        <include refid="order_quick_filter_key_type"/>
        <include refid="order_quick_filter_key_type_new"/>

        <include refid="order_query_new"/>

        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@TRADE_CREATE_TIME">
            AND `order`.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@PAY_TIME">
            AND `order`.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@DOWNLOAD_TIME">
            AND `order`.create_time between #{beginTime} and #{endTime}
        </if>
        <if test="intSellerFlag !=null and intSellerFlag>0">
            AND `order`.seller_flag=#{intSellerFlag}
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND `order`.mapping_state=1
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND `order`.mapping_state=0
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@Submit">
            AND `order`.process_state=1 AND `order`.mapping_state=1
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@NoSubmit">
            AND `order`.process_state=0
            AND `order`.mapping_state=1 AND `order`.order_deliver_required=1
            AND `order`.local_trade_state!=5 AND `order`.order_deliver_required=1
        </if>
        <if test="filterParameter != null">
            <include refid="order_filter"/>
        </if>
        <if test="btypeIds!=null and btypeIds.size() > 0">
            AND `order`.btype_id IN
            <foreach collection="btypeIds" close=")" open="(" separator="," item="btypeId">
                #{btypeId}
            </foreach>
        </if>
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            AND `order`.id IN
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="deleted != null ">
            AND `order`.deleted= #{deleted}
        </if>
        <if test="isDraft !=null and isDraft.size()>0 ">
            AND `extend`.is_draft IN
            <foreach collection="isDraft" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="quickFilterTypes !=null and quickFilterTypes.size() > 0 ">
            AND ( 1!=1
            <foreach collection="quickFilterTypes" close="" open="" separator="" item="quickFilterType">
                <include refid="order_quick_filter_or"/>
            </foreach>
            )
        </if>
        <if test="filterParameter != null">
            <include refid="order_quick_filter"/>
        </if>
        <if test="queryCyclePurchaseMainOrder!=null and queryCyclePurchaseMainOrder">
            AND (`order`.order_sale_type != 3 OR (`order`.order_sale_type=3 AND `order`.platform_parent_order_id=''))
        </if>
        <if test="filterParameter == null or (filterParameter != null and filterParameter.defaultSort)">
            order by `order`.trade_pay_time+0 desc
        </if>
    </select>
    <sql id="order_filter">
        <if test="filterParameter.intSellerFlag !=null">
            AND `order`.seller_flag=#{filterParameter.intSellerFlag}
        </if>
        <if test="filterParameter.mentionValue !=null ">
            AND `order`.id IN ( SELECT order_id FROM `pl_eshop_order_mark` AS mark
            WHERE mark.order_id = `order`.id AND mark.profile_id=`order`.profile_id
            AND mark.`mark_code` = #{filterParameter.mentionValue})
        </if>
        <if test="filterParameter.tradeOrderId != null and filterParameter.tradeOrderId != ''">
            AND `order`.trade_order_id like CONCAT('%',#{filterParameter.tradeOrderId},'%')
        </if>
        <if test="filterParameter.orderSourceType != null">
            AND `order`.order_source_type=#{filterParameter.orderSourceType}
        </if>
        <if test="filterParameter.businessType != null">
            AND `order`.business_type=#{filterParameter.businessType}
        </if>
        <if test="filterParameter.localTradeState != null">
            AND `order`.local_trade_state=#{filterParameter.localTradeState}
        </if>
        <if test="filterParameter.platformParentOrderId != null">
            AND `order`.platform_parent_order_id like CONCAT('%',#{filterParameter.platformParentOrderId},'%')
        </if>
        <if test="filterParameter.localRefundState != null">
            AND `order`.local_refund_state=#{filterParameter.localRefundState}
        </if>
        <if test="filterParameter.buyerMessage != null and filterParameter.buyerMessage != ''">
            AND `order`.buyer_message like CONCAT('%',#{filterParameter.buyerMessage},'%')
        </if>
        <if test="filterParameter.sellerMemo != null and filterParameter.sellerMemo != ''">
            AND `order`.seller_memo like CONCAT('%',#{filterParameter.sellerMemo},'%')
        </if>
        <if test="filterParameter.creatorName != null and filterParameter.creatorName != ''">
            AND `creator`.fullname like CONCAT('%',#{filterParameter.creatorName},'%')
        </if>
        <if test="filterParameter.otypeName != null and filterParameter.otypeName != ''">
            AND eshop.fullname like CONCAT('%',#{filterParameter.otypeName},'%')
        </if>
        <if test="filterParameter.otypeId != null">
            AND `order`.otype_id = #{filterParameter.otypeId}
        </if>
        <if test="filterParameter.processState != null">
            AND `order`.process_state=#{filterParameter.processState}
        </if>
        <if test="filterParameter.reSendState != null">
            AND `order`.re_send_state=#{filterParameter.reSendState}
        </if>
        <if test="filterParameter.payTimeType != null">
            AND `order`.pay_time_type=#{filterParameter.payTimeType}
        </if>
        <if test="filterParameter.ocategory != null">
            AND otype.ocategory=#{filterParameter.ocategory}
        </if>
        <if test="filterParameter.btypeName != null and filterParameter.btypeName != ''">
            AND btype.fullname like CONCAT('%',#{filterParameter.btypeName},'%')
        </if>
        <if test="filterParameter.ktypeName != null and filterParameter.ktypeName != ''">
            AND ktype.fullname like CONCAT('%',#{filterParameter.ktypeName},'%')
        </if>
        <if test="filterParameter.deliverType != null">
            AND `order`.deliver_type=#{filterParameter.deliverType}
        </if>
        <if test="filterParameter.invoiceRequired != null">
            AND invoice.invoice_required=#{filterParameter.invoiceRequired}
        </if>
        <if test="filterParameter.customerShopAccount != null and filterParameter.customerShopAccount != ''">
            AND buyer.customer_shop_account like CONCAT('%',#{filterParameter.customerShopAccount},'%')
        </if>
        <if test="filterParameter.enableDicAccount != null and filterParameter.enableDicAccount
         and filterParameter.keyArrays!=null and filterParameter.keyArrays.size()>0">
            and (
            buyer.ai in
            <foreach collection="filterParameter.keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            <if test="filterParameter.sisTradeOrderIds!=null and filterParameter.sisTradeOrderIds.size()>0">
                or trade_order_id in
                <foreach collection="filterParameter.sisTradeOrderIds" close=")" open="(" separator="," item="sisKey">
                    #{sisKey}
                </foreach>
            </if>
            )
        </if>
        <if test="filterParameter.fullBuyerInfo != null and filterParameter.fullBuyerInfo != ''">
            AND
            CONCAT(
            IFNULL(buyer.`customer_shop_account`,''),
            " ",
            IFNULL(buyer.`customer_receiver`,''),
            " ",
            IFNULL(buyer.`customer_receiver_mobile`,''),
            " ",
            IFNULL(buyer.`customer_receiver_phone`,''),
            " ",
            IFNULL(buyer.`customer_receiver_province`,''),
            " ",
            IFNULL(buyer.`customer_receiver_city`,''),
            " ",
            IFNULL(buyer.`customer_receiver_district`,''),
            " ",
            IFNULL(buyer.`customer_receiver_town`,''),
            " ",
            IFNULL(buyer.`customer_receiver_address`,'')
            )
            LIKE CONCAT('%', #{filterParameter.fullBuyerInfo}, '%')
        </if>
        <if test="filterParameter.createType != null">
            AND `order`.create_type=#{filterParameter.createType}
        </if>
        <if test="filterParameter.collectCustomer != null and filterParameter.collectCustomer != ''">
            AND extend.collect_customer like CONCAT('%',#{filterParameter.collectCustomer},'%')
        </if>
        <if test="filterParameter.salesman != null and filterParameter.salesman != ''">
            AND `order`.salesman like CONCAT('%',#{filterParameter.salesman},'%')
        </if>
        <if test="filterParameter.paymentMode != null">
            AND extend.payment_mode=#{filterParameter.paymentMode}
        </if>
        <if test="filterParameter.etypeName != null and filterParameter.etypeName != ''">
            AND etype.fullname like CONCAT('%',#{filterParameter.etypeName},'%')
        </if>
        <if test="filterParameter.localFreightName != null and filterParameter.localFreightName != ''">
            AND `order`.local_freight_name like CONCAT('%',#{filterParameter.localFreightName},'%')
        </if>
        <if test="filterParameter.localFreightBillNo != null and filterParameter.localFreightBillNo != ''">
            AND `order`.local_freight_bill_no like CONCAT('%',#{filterParameter.localFreightBillNo},'%')
        </if>
        <if test="filterParameter.customerExpectedFreightName != null and filterParameter.customerExpectedFreightName != ''">
            AND `order`.customer_expected_freight_name like
            CONCAT('%',#{filterParameter.customerExpectedFreightName},'%')
        </if>
        <if test="filterParameter.remark != null and filterParameter.remark != ''">
            AND `order`.remark like CONCAT('%',#{filterParameter.remark},'%')
        </if>
        <if test="filterParameter.platformStockId != null and filterParameter.platformStockId != ''">
            AND `order`.platform_stock_id like CONCAT('%',#{filterParameter.platformStockId},'%')
        </if>
        <if test="filterParameter.platformStockName != null and filterParameter.platformStockName != ''">
            AND `smap`.platform_store_name like CONCAT('%',#{filterParameter.platformStockName},'%')
        </if>
        <if test="filterParameter.exportState != null">
            <if test="filterParameter.exportState == 0">
                AND extend.export_count = 0
            </if>
            <if test="filterParameter.exportState != 0">
                AND extend.export_count &gt; 0
            </if>
        </if>
        <if test="filterParameter.buyerTradeMinTotal != null">
            AND `order`.buyer_trade_total &gt;= #{filterParameter.buyerTradeMinTotal}
        </if>
        <if test="filterParameter.buyerTradeMaxTotal != null">
            AND `order`.buyer_trade_total &lt;= #{filterParameter.buyerTradeMaxTotal}
        </if>
        <if test="filterParameter.buyerPaidMinTotal != null">
            AND `order`.buyer_paid_total &gt;= #{filterParameter.buyerPaidMinTotal}
        </if>
        <if test="filterParameter.buyerPaidMaxTotal != null">
            AND `order`.buyer_paid_total &lt;= #{filterParameter.buyerPaidMaxTotal}
        </if>
        <if test="filterParameter.buyerUnpaidMinTotal != null">
            AND `order`.buyer_unpaid_total &gt;= #{filterParameter.buyerUnpaidMinTotal}
        </if>
        <if test="filterParameter.buyerUnpaidMaxTotal != null">
            AND `order`.buyer_unpaid_total &lt;= #{filterParameter.buyerUnpaidMaxTotal}
        </if>
        <if test="filterParameter.distributionDisedTaxedMinTotal != null">
            AND (
            (`order`.distribution_dised_taxed_total = 0 and `order`.dised_taxed_total &gt;=
            #{filterParameter.distributionDisedTaxedMinTotal})
            or
            (`order`.distribution_dised_taxed_total &gt;= #{filterParameter.distributionDisedTaxedMinTotal} and
            `order`.dised_taxed_total = 0)
            )
        </if>
        <if test="filterParameter.distributionDisedTaxedMaxTotal != null">
            AND (
            (`order`.distribution_dised_taxed_total = 0 and `order`.dised_taxed_total &lt;=
            #{filterParameter.distributionDisedTaxedMaxTotal})
            or
            (`order`.distribution_dised_taxed_total &lt;= #{filterParameter.distributionDisedTaxedMaxTotal} and
            `order`.dised_taxed_total = 0)
            )
        </if>
        <if test="filterParameter.disedTaxedMinTotal != null">
            AND `order`.dised_taxed_total &gt;= #{filterParameter.disedTaxedMinTotal}
        </if>
        <if test="filterParameter.disedTaxedMaxTotal != null">
            AND `order`.dised_taxed_total &lt;= #{filterParameter.disedTaxedMaxTotal}
        </if>
        <if test="filterParameter.taxMinTotal != null">
            AND `order`.tax_total &gt;= #{filterParameter.taxMinTotal}
        </if>
        <if test="filterParameter.taxMaxTotal != null">
            AND `order`.tax_total &lt;= #{filterParameter.taxMaxTotal}
        </if>
        <if test="filterParameter.sellerPreferentialMinTotal != null">
            AND `order`.order_preferential_allot_total &gt;= #{filterParameter.sellerPreferentialMinTotal}
        </if>
        <if test="filterParameter.sellerPreferentialMaxTotal != null">
            AND `order`.order_preferential_allot_total &lt;= #{filterParameter.sellerPreferentialMaxTotal}
        </if>
        <if test="filterParameter.platformPreferentialMinTotal != null">
            AND `order`.platform_order_preferential_total &gt;= #{filterParameter.platformPreferentialMinTotal}
        </if>
        <if test="filterParameter.platformPreferentialMaxTotal != null">
            AND `order`.platform_order_preferential_total &lt;= #{filterParameter.platformPreferentialMaxTotal}
        </if>
        <if test="filterParameter.advanceTotalMinTotal != null">
            AND `extend`.advance_total &gt;= #{filterParameter.advanceTotalMinTotal}
        </if>
        <if test="filterParameter.advanceTotalMaxTotal != null">
            AND `extend`.advance_total &lt;= #{filterParameter.advanceTotalMaxTotal}
        </if>
        <if test="filterParameter.buyerFreightMinFee != null">
            AND `order`.order_buyer_freight_fee &gt;= #{filterParameter.buyerFreightMinFee}
        </if>
        <if test="filterParameter.buyerFreightMaxFee != null">
            AND `order`.order_buyer_freight_fee &lt;= #{filterParameter.buyerFreightMaxFee}
        </if>
        <if test="filterParameter.serviceMinFee != null">
            AND `order`.ptype_service_fee &gt;= #{filterParameter.serviceMinFee}
        </if>
        <if test="filterParameter.serviceMaxFee != null">
            AND `order`.ptype_service_fee &lt;= #{filterParameter.serviceMaxFee}
        </if>
        <if test="filterParameter.platformOrderSubsidyTotalMinTotal != null">
            AND `extend`.platform_order_subsidy_total &gt;= #{filterParameter.platformOrderSubsidyTotalMinTotal}
        </if>
        <if test="filterParameter.platformOrderSubsidyTotalMaxTotal != null">
            AND `extend`.platform_order_subsidy_total &lt;= #{filterParameter.platformOrderSubsidyTotalMaxTotal}
        </if>
        <if test="filterParameter.anchorOrderPreferentialTotalMinTotal != null">
            AND `extend`.anchor_order_preferential_total &gt;= #{filterParameter.anchorOrderPreferentialTotalMinTotal}
        </if>
        <if test="filterParameter.anchorOrderPreferentialTotalMaxTotal != null">
            AND `extend`.anchor_order_preferential_total &lt;= #{filterParameter.anchorOrderPreferentialTotalMaxTotal}
        </if>
        <if test="filterParameter.promisedSignStartTimeBeginTime != null and filterParameter.promisedSignStartTimeEndTime != null">
            AND timing.promised_sign_startTime between #{filterParameter.promisedSignStartTimeBeginTime} and
            #{filterParameter.promisedSignStartTimeEndTime}
        </if>
        <if test="filterParameter.planSendBeginTime != null and filterParameter.planSendEndTime != null">
            AND timing.plan_send_time between #{filterParameter.planSendBeginTime} and
            #{filterParameter.planSendEndTime}
        </if>
        <if test="filterParameter.promisedSignBeginTime != null and filterParameter.promisedSignEndTime != null">
            AND timing.promised_sign_time between #{filterParameter.promisedSignBeginTime} and
            #{filterParameter.promisedSignEndTime}
        </if>
        <if test="filterParameter.planSignTimeBeginTime != null and filterParameter.planSignTimeEndTime != null">
            AND timing.plan_sign_time between #{filterParameter.planSignTimeBeginTime} and
            #{filterParameter.planSignTimeEndTime}
        </if>
        <if test="filterParameter.sendBeginTime != null and filterParameter.sendEndTime != null">
            AND timing.send_time between #{filterParameter.sendBeginTime} and #{filterParameter.sendEndTime}
        </if>
        <if test="filterParameter.promisedCollectBeginTime != null and filterParameter.promisedCollectEndTime != null">
            AND timing.promised_collect_time between #{filterParameter.promisedCollectBeginTime} and
            #{filterParameter.promisedCollectEndTime}
        </if>
        <if test="filterParameter.createBeginTime != null and filterParameter.createEndTime != null">
            AND `order`.create_time between #{filterParameter.createBeginTime} and #{filterParameter.createEndTime}
        </if>
        <if test="filterParameter.tradeCreateBeginTime != null and filterParameter.tradeCreateEndTime != null">
            AND `order`.trade_create_time between #{filterParameter.tradeCreateBeginTime} and
            #{filterParameter.tradeCreateEndTime}
        </if>
        <if test="filterParameter.tradePayBeginTime != null and filterParameter.tradePayEndTime != null">
            AND `order`.trade_pay_time between #{filterParameter.tradePayBeginTime} and
            #{filterParameter.tradePayEndTime}
        </if>
        <if test="filterParameter.tradeFinishBeginTime != null and filterParameter.tradeFinishEndTime != null">
            AND `order`.trade_finish_time between #{filterParameter.tradeFinishBeginTime} and
            #{filterParameter.tradeFinishEndTime}
        </if>
        <if test="filterParameter.collectBeginTime != null and filterParameter.collectEndTime != null">
            AND extend.collect_time between #{filterParameter.collectBeginTime} and #{filterParameter.collectEndTime}
        </if>
        <if test="filterParameter.installationServiceProvider != null and filterParameter.installationServiceProvider != ''">
            AND extend.installation_service_provider like CONCAT('%',#{filterParameter.installationServiceProvider},'%')
        </if>
        <if test="filterParameter.invoiceState != null">
            AND invoice.invoice_state=#{filterParameter.invoiceState}
        </if>
        <if test="filterParameter.selfDeliveryMode != null">
            AND `order`.self_delivery_mode=#{filterParameter.selfDeliveryMode}
        </if>
        <if test="filterParameter.groupHeaderName != null and filterParameter.groupHeaderName != ''">
            AND `extend`.group_header_name like CONCAT('%',#{filterParameter.groupHeaderName},'%')
        </if>
        <if test="filterParameter.sellerFlagMemo != null and filterParameter.sellerFlagMemo != ''">
            AND `extend`.seller_flag_memo like CONCAT('%',#{filterParameter.sellerFlagMemo},'%')
        </if>
        <if test="filterParameter.flowChannel != null and filterParameter.flowChannel != ''">
            AND `extend`.flow_channel like CONCAT('%',#{filterParameter.flowChannel},'%')
        </if>
        <if test="filterParameter.gatherStatus != null and filterParameter.gatherStatus != @com.wsgjp.ct.sale.biz.eshoporder.entity.enums.GatherStatusEnum@ALL">
            AND `extend`.gather_status = #{filterParameter.gatherStatus}
        </if>
        <if test="filterParameter.confirmStatus != null">
            AND `extend`.confirm_status = #{filterParameter.confirmStatus}
        </if>
        <if test="filterParameter.logisticsStatus != null">
            AND `extend`.logistics_status = #{filterParameter.logisticsStatus}
        </if>
        <if test="filterParameter.platformQcResult != null">
            AND `extend`.platform_qc_result = #{filterParameter.platformQcResult}
        </if>
        <if test="filterParameter.platformIdentifyResult != null">
            AND `extend`.platform_identify_result = #{filterParameter.platformIdentifyResult}
        </if>
        <if test="filterParameter.distributionBuyerTradeId != null and filterParameter.distributionBuyerTradeId != ''">
            AND `distribution`.distribution_buyer_trade_id like
            CONCAT('%',#{filterParameter.distributionBuyerTradeId},'%')
        </if>
        <if test="filterParameter.groupTitle != null and filterParameter.groupTitle != ''">
            AND `extend`.group_title like CONCAT('%',#{filterParameter.groupTitle},'%')
        </if>
        <if test="filterParameter.signBeginTime != null and filterParameter.signEndTime != null">
            AND timing.sign_time between #{filterParameter.signBeginTime} and #{filterParameter.signEndTime}
        </if>
        <if test="filterParameter.isDraft != -1 ">
            AND extend.is_draft = #{filterParameter.isDraft}
        </if>
        <if test="filterParameter.nationalSubsidyMinTotal != null">
            AND `extend`.national_subsidy_total &gt;= #{filterParameter.nationalSubsidyMinTotal}
        </if>
        <if test="filterParameter.nationalSubsidyMaxTotal != null">
            AND `extend`.national_subsidy_total &lt;= #{filterParameter.nationalSubsidyMaxTotal}
        </if>
    </sql>

    <sql id="order_quick_filter">
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@WAIT_SUBMIT and businessFilterQuery">
            AND `order`.process_state =0
            AND `order`.mapping_state=1 AND `order`.local_trade_state IN(2) AND `order`.order_deliver_required=1
            AND `order`.local_refund_state=0
            <if test="!compelSubmit">
                AND `order`.deleted=0
            </if>
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@UNKNOWN_STATE">
            AND `order`.local_trade_state=0 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@NO_STOCK">
            AND `order`.ktype_id=0 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5 AND
            `order`.deleted = 0
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@OTHER">
            AND (`order`.order_deliver_required=0 OR `order`.local_trade_state = 5 OR `order`.deleted = 1)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@FAIL_ORDER">
            AND (`order`.local_refund_state in (1,2,3,4) OR `order`.deleted=1 OR `order`.local_trade_state = 0
            OR `order`.ktype_id=0 OR `order`.mapping_state=0 ) AND `order`.order_deliver_required=1 AND
            `order`.local_trade_state != 5
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@NORMAL_ORDER">
            AND `order`.local_refund_state=0 AND `order`.deleted=0 AND `order`.local_trade_state IN (1,2,3,4,6,7)
            AND `order`.ktype_id !=0 AND `order`.mapping_state=1 AND `order`.order_deliver_required=1
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@REFUNDING">
            AND `order`.local_refund_state=1 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@REFUND_SUCCESS">
            AND `order`.local_refund_state=4 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@EXCHANGING_REPLENISHING">
            AND `order`.re_send_state=1 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@EXCHANGED_REPLENISHED">
            AND `order`.re_send_state=2 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@PART_REFUNDING">
            AND `order`.local_refund_state=2 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@PART_REFUNDED">
            AND `order`.local_refund_state=3 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@NOT_MAPPING">
            AND `order`.mapping_state=0 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5 AND
            `order`.deleted = 0
            <if test="insertUnRelationByNormalOrder">
                AND `order`.local_refund_state !=4 AND not exists(select 1
                from pl_eshop_order_mark m
                where m.profile_id = `order`.profile_id
                and m.mark_target = 0
                and m.order_id = `order`.id
                and m.mark_code = 90870001)
            </if>
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@WAIT_SUBMIT and !businessFilterQuery">
            AND `order`.process_state = 0
            AND `order`.local_refund_state=0 AND `order`.deleted=0 AND `order`.local_trade_state IN (1,2,3,4,6,7)
            AND `order`.ktype_id !=0 AND `order`.mapping_state=1 AND `order`.order_deliver_required=1
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@SUBMIT">
            AND `order`.process_state in (1,2)
            AND `order`.local_refund_state=0 AND `order`.deleted=0 AND `order`.local_trade_state IN (1,2,3,4,6,7)
            AND `order`.ktype_id !=0 AND `order`.mapping_state=1 AND `order`.order_deliver_required=1
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@CLOSE_STATE">
            AND `order`.local_trade_state=5
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@NO_DELIVER">
            AND `order`.order_deliver_required =0
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@DELETED">
            AND `order`.deleted =1
        </if>

        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@WAIT_SUBMIT_NEW">
            AND (`order`.process_state = 0 AND `order`.local_trade_state IN (0,2) AND `order`.order_deliver_required=1
            AND `order`.local_refund_state IN (0,1,2)
            AND `order`.deleted=0)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@REFUNDING_NEW">
            AND (`order`.local_refund_state IN (1,2) AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5)
        </if>
    </sql>

    <sql id="order_quick_filter_or">
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@WAIT_SUBMIT and businessFilterQuery">
            OR (`order`.process_state = 0
            AND `order`.mapping_state=1 AND `order`.local_trade_state IN(2) AND `order`.order_deliver_required=1
            AND `order`.local_refund_state=0
            <if test="!compelSubmit">
                AND `order`.deleted=0
            </if>)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@UNKNOWN_STATE">
            OR (`order`.local_trade_state=0 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@NO_STOCK">
            OR (`order`.ktype_id=0 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5 AND
            `order`.deleted = 0)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@OTHER">
            OR (`order`.order_deliver_required=0 OR `order`.local_trade_state = 5 OR `order`.deleted = 1)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@FAIL_ORDER">
            OR ((`order`.local_refund_state in (1,2,3,4) OR `order`.deleted=1 OR `order`.local_trade_state = 0
            OR `order`.ktype_id=0 OR `order`.mapping_state=0 ) AND `order`.order_deliver_required=1 AND
            `order`.local_trade_state != 5)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@NORMAL_ORDER">
            OR (`order`.local_refund_state=0 AND `order`.deleted=0 AND `order`.local_trade_state IN (1,2,3,4,6,7)
            AND `order`.ktype_id !=0 AND `order`.mapping_state=1 AND `order`.order_deliver_required=1)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@REFUNDING">
            OR (`order`.local_refund_state=1 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@REFUND_SUCCESS">
            OR (`order`.local_refund_state=4 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@EXCHANGING_REPLENISHING">
            OR( `order`.re_send_state=1 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@EXCHANGED_REPLENISHED">
            OR( `order`.re_send_state=2 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@PART_REFUNDING">
            OR (`order`.local_refund_state=2 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@PART_REFUNDED">
            OR (`order`.local_refund_state=3 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@NOT_MAPPING">
            OR (`order`.mapping_state=0 AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5 AND
            `order`.deleted = 0
            <if test="insertUnRelationByNormalOrder">
                AND `order`.local_refund_state !=4 AND not exists(select 1
                from pl_eshop_order_mark m
                where m.profile_id = `order`.profile_id
                and m.mark_target = 0
                and m.order_id = `order`.id
                and m.mark_code = 90870001)
            </if>
            )
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@WAIT_SUBMIT and !businessFilterQuery">
            OR (`order`.process_state = 0
            AND `order`.local_refund_state=0 AND `order`.deleted=0 AND `order`.local_trade_state IN (1,2,3,4,6,7)
            AND `order`.ktype_id !=0 AND `order`.mapping_state=1 AND `order`.order_deliver_required=1)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@SUBMIT">
            OR (`order`.process_state in (1,2)
            AND `order`.local_refund_state=0 AND `order`.deleted=0 AND `order`.local_trade_state IN (1,2,3,4,6,7)
            AND `order`.ktype_id !=0 AND `order`.mapping_state=1 AND `order`.order_deliver_required=1)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@CLOSE_STATE">
            OR (`order`.local_trade_state=5)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@NO_DELIVER">
            OR (`order`.order_deliver_required =0)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@DELETED">
            OR (`order`.deleted =1)
        </if>

        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@WAIT_SUBMIT_NEW">
            OR (`order`.process_state = 0 AND `order`.local_trade_state IN (0,2) AND `order`.order_deliver_required=1
            AND `order`.local_refund_state IN (0,1,2)
            AND `order`.deleted=0)
        </if>
        <if test="quickFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickFilterType@REFUNDING_NEW">
            OR (`order`.local_refund_state IN (1,2) AND `order`.order_deliver_required=1 AND `order`.local_trade_state != 5)
        </if>
    </sql>

    <select id="querySaleOrderFullInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        select od.*,e.fullname as otypeName,bo.ocategory,
        e.eshop_type as shopType,abb.pr_total as btypePrTotal,ec.btype_generate_type,
        be.fullname as etypeName,bk.fullname as ktypeName,
        bb.fullname as btypeName,
        bd.fullname as departmentName,
        `timing`.eshop_order_id AS 'timing.eshop_order_id',
        `timing`.profile_id AS 'timing.profile_id',
        `timing`.cn_service AS 'timing.cn_service',
        `timing`.plan_send_time AS 'timing.plan_send_time',
        `timing`.send_time AS 'timing.send_time',
        `timing`.system_timing AS 'timing.system_timing',
        `timing`.create_time AS 'timing.create_time',
        `timing`.update_time AS 'timing.update_time',
        `timing`.timing_type AS 'timing.timing_type',
        `extend`.supplier_id AS 'extend.supplier_id',
        `bd`.fullname AS 'extend.supplier_name',
        `extend`.installation_service_provider AS 'extend.installation_service_provider',
        IF(TO_DAYS(`extend`.collect_time)=TO_DAYS('1990-01-01'),'',`extend`.collect_time) AS 'extend.collectTime',
        `extend`.collect_customer AS 'extend.collect_customer',
        `extend`.gather_status AS 'extend.gather_status',
        `extend`.payment_mode AS 'extend.payment_mode',
        `extend`.platform_required AS 'extend.platform_required',
        `extend`.group_header_name AS 'extend.group_header_name',
        `extend`.buyer_unique_mark AS 'extend.buyer_unique_mark',
        `extend`.trade_summary_md5 AS 'extend.trade_summary_md5',
        `extend`.advance_total AS 'extend.advance_total',
        `extend`.pickup_code AS 'extend.pickup_code',
        `extend`.platform_dispatcher_name AS 'extend.platform_dispatcher_name',
        `extend`.platform_dispather_mobile AS 'extend.platform_dispather_mobile',
        `extend`.logistics_status AS 'extend.logistics_status',
        `extend`.confirm_status AS 'extend.confirm_status',
        `extend`.pick_up_address_id AS 'extend.pick_up_address_id',
        `extend`.real_buyer_id AS 'extend.real_buyer_id',
        `extend`.platform_qc_result AS 'extend.platform_qc_result',
        `extend`.platform_identify_result AS 'extend.platform_identify_result',
        `extend`.flow_channel AS 'extend.flow_channel',
        `extend`.mall_deduction_fee AS 'extend.mall_deduction_fee',
        `extend`.seller_flag_memo AS 'extend.seller_flag_memo',
        `extend`.group_title AS 'extend.group_title',
        `extend`.group_no AS 'extend.group_no',
        `extend`.hold_time AS 'extend.hold_time',
        `extend`.take_goods_code AS 'extend.take_goods_code',
        `extend`.platform_quality_org_id AS 'extend.platform_quality_org_id',
        `extend`.platform_quality_org_name AS 'extend.platform_quality_org_name',
        `extend`.platform_quality_warehouse_code AS 'extend.platform_quality_warehouse_code',
        `extend`.platform_quality_warehouse_name AS 'extend.platform_quality_warehouse_name',
        `extend`.is_draft AS 'extend.is_draft'
        from pl_eshop_sale_order od
        left join pl_eshop_sale_order_timing timing on timing.profile_id=od.profile_id and timing.eshop_order_id=od.id
        left join pl_eshop_sale_order_extend extend on extend.profile_id=od.profile_id and extend.eshop_order_id=od.id
        left join pl_eshop e on e.profile_id=od.profile_id and e.otype_id=od.otype_id
        left join base_otype bo on bo.profile_id=od.profile_id and bo.id=od.otype_id
        left join base_etype be on be.profile_id=od.profile_id and be.id=od.etype_id
        left join base_ktype bk on bk.profile_id=od.profile_id and bk.id=od.ktype_id
        left join base_btype bb on bb.profile_id=od.profile_id and bb.id=od.btype_id
        left join base_btype bs on bs.profile_id=od.profile_id and bs.id=od.btype_id
        left join base_btype bd on bd.profile_id=od.profile_id and bd.id=extend.supplier_id
        left join acc_btype_balance abb on abb.profile_id=od.profile_id and abb.btype_id=od.btype_id
        left join pl_eshop_config ec on ec.profile_id=od.profile_id and ec.eshop_id=od.otype_id
        where od.profile_id=#{profileId}
        <if test="tradeOrderId!=null and tradeOrderId!=''">
            and od.trade_order_id=#{tradeOrderId}
        </if>
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and od.id=#{eshopOrderId}
        </if>
        <if test="otypeIds!=null and otypeIds.size()>0">
            AND `od`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeid">
                #{otypeid}
            </foreach>
        </if>
        limit 1
    </select>

    <select id="querySaleOrderDetailsSerialno"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetailSerialno">
        select *
        from pl_eshop_sale_order_detail_serialno
        where profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            And eshop_order_id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>

    <select id="querySaleOrderDetailsGiftRelation"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetailGiftRelation">
        select *
        from pl_eshop_saleorder_detail_gift_relation
        where profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            And eshop_order_id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>

    <select id="querySaleOrderDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail">
        select 0 AS selected, d.*,od.btype_id,od.ktype_id,od.platform_parent_order_id,
        bp.fullname as ptypeName,bp.pcategory,bp.ptype_type,bp.ptype_area,bp.shortname as ptypeShortName,bp.usercode as
        ptypeCode,
        bp.ptype_width,bp.ptype_length,bp.ptype_height,bp.weight as
        ptypeWeight,bp.batchenabled,bp.snenabled,bp.propenabled,
        bp.sub_unit as subUnit,
        bp.length_unit,bp.weight_unit,bp.standard as ptypeStandard,bp.memo as ptypeMemo,
        <if test="submitNotNeedXcode == false">
            bpx.xcode as xcode,
        </if>
        bpf.fullbarcode as barcode,
        bps.prop_name1,bps.propvalue_name1,bps.prop_id1,bps.propvalue_id1,
        bps.prop_name2,bps.propvalue_name2,bps.prop_id2,bps.propvalue_id2,
        bps.prop_name3,bps.propvalue_name3,bps.prop_id3,bps.propvalue_id3,
        bps.prop_name4,bps.propvalue_name4,bps.prop_id4,bps.propvalue_id4,
        bps.prop_name5,bps.propvalue_name5,bps.prop_id5,bps.propvalue_id5,
        bps.prop_name6,bps.propvalue_name6,bps.prop_id6,bps.propvalue_id6,
        unit.unit_name as unitName,unit.unit_rate as unitRate,
        bsunit.unit_name as baseUnitName,brand.brand_name,
        (CASE WHEN bps.pic_url='' THEN pic.pic_url ELSE bps.pic_url END) pic_url,o.ocategory,
        dpurchase.eshop_order_id as `purchase.eshop_order_id`,
        dpurchase.profile_id as `purchase.profile_id`,
        dpurchase.eshop_order_detail_id as `purchase.eshop_order_detail_id`,
        dpurchase.purchase_price as `purchase.purchase_price`,
        dpurchase.purchase_total as `purchase.purchase_total`,
        dpurchase.purchase_tax_rate as `purchase.purchase_tax_rate`,
        dpurchase.purchase_tax_total as `purchase.purchase_tax_total`,
        dpurchase.purchase_dised_price as `purchase.purchase_dised_price`,
        dpurchase.purchase_dised_total as `purchase.purchase_dised_total`,
        dpurchase.purchase_discount as `purchase.purchase_discount`,
        dpurchase.purchase_dised_taxed_price as `purchase.purchase_dised_taxed_price`,
        dpurchase.purchase_dised_taxed_total as `purchase.purchase_dised_taxed_total`,
        `distribution`.profile_id AS 'distribution.profile_id',
        `distribution`.eshop_order_id AS 'distribution.eshop_order_id',
        `distribution`.eshop_order_detail_id AS 'distribution.eshop_order_detail_id',
        `distribution`.eshop_id AS 'distribution.eshop_id',
        `distribution`.buyer_price AS 'distribution.buyer_price',
        `distribution`.buyer_total AS 'distribution.buyer_total',
        `distribution`.buyer_discount AS 'distribution.buyer_discount',
        `distribution`.buyer_dised_initial_price AS 'distribution.buyer_dised_initial_price',
        `distribution`.buyer_dised_initial_total AS 'distribution.buyer_dised_initial_total',
        `distribution`.buyer_ptype_preferential_total AS 'distribution.buyer_ptype_preferential_total',
        `distribution`.buyer_order_preferential_allot_total AS 'distribution.buyer_order_preferential_allot_total',
        IF( `od`.business_type != 203, d.dised_taxed_price, `distribution`.buyer_dised_taxed_price) AS
        'distribution.buyer_dised_taxed_price',
        IF( `od`.business_type != 203, d.dised_taxed_total, `distribution`.buyer_dised_taxed_total) AS
        'distribution.buyer_dised_taxed_total',
        `distribution`.buyer_tax_rate AS 'distribution.buyer_tax_rate',
        `distribution`.buyer_tax_total AS 'distribution.buyer_tax_total',
        `distribution`.buyer_dised_price AS 'distribution.buyer_dised_price',
        `distribution`.buyer_dised_total AS 'distribution.buyer_dised_total',
        `distribution`.distribution_buyer_trade_detail_id AS 'distribution.distribution_buyer_trade_detail_id',
        case when IFNULL(`pepm`.mark_code,1008) = 1008 then 0 else 1 end isSaleQtyNeedToOccupy,
        `liveBroadcast`.profile_id as 'liveBroadcast.profileId',
        `liveBroadcast`.eshop_id as 'liveBroadcast.eshopId',
        `liveBroadcast`.eshop_order_id as 'liveBroadcast.eshopOrderId',
        `liveBroadcast`.detail_id as 'liveBroadcast.detailId',
        `liveBroadcast`.platform_anchor_name as 'liveBroadcast.platformAnchorName',
        `liveBroadcast`.platform_anchor_id as 'liveBroadcast.platformAnchorId',
        `liveBroadcast`.platform_live_room_id as 'liveBroadcast.platformLiveRoomId',
        `liveBroadcast`.live_brodcast_session_id as 'liveBroadcast.liveBrodcastSessionId',
        `liveBroadcast`.create_time as 'liveBroadcast.createTime',
        `liveBroadcast`.update_time as 'liveBroadcast.updateTime',
        `batch`.id as 'batch.id',
        `batch`.eshop_order_id as 'batch.eshopOrderId',
        `batch`.eshop_order_detail_id as 'batch.eshopOrderDetailId',
        `batch`.profile_id as 'batch.profileId',
        `batch`.batchno as 'batch.batchno',
        `batch`.produce_date as 'batch.produceDate',
        `batch`.expire_date as 'batch.expireDate',
        `batch`.qty as 'batch.qty',
        `batch`.sub_qty as 'batch.subQty',
        `batch`.unit_qty as 'batch.unitQty',
        `batch`.batch_price as 'batch.batchPrice',
        `batch`.create_time as 'batch.createTime',
        `batch`.update_time as 'batch.updateTime',
        detailExtend.mall_deduction_rate as 'extend.mallDeductionRate',
        detailExtend.mall_deduction_fee as 'extend.mallDeductionFee',
        detailExtend.anchor_order_preferential_total as 'extend.anchor_order_preferential_total',
        detailExtend.anchor_ptype_preferential_total as 'extend.anchor_ptype_preferential_total',
        detailExtend.platform_order_subsidy_total as 'extend.platform_order_subsidy_total',
        detailExtend.platform_ptype_subsidy_total as 'extend.platform_ptype_subsidy_total',
        detailExtend.national_subsidy_total as 'extend.national_subsidy_total',
        detailExtend.gift as 'extend.gift',
        timing.profile_id as 'timing.profileId',
        timing.eshop_order_id as 'timing.eshopOrderId',
        timing.eshop_order_detail_id as 'timing.eshopOrderDetailId',
        timing.id as 'timing.id',
        timing.promised_send_time as 'timing.promisedSendTime',
        timing.create_time as 'timing.createTime',
        timing.update_time as 'timing.updateTime',
        `smap`.platform_store_name AS 'platform_stock_name',
        bk.fullname as ktypeName,
        distribution.buyer_dised_taxed_price distributionDisedTaxedPrice,
        distribution.buyer_dised_taxed_total distributionDisedTaxedTotal,
        od.trade_order_id,
        d.platform_order_preferential_total,
        detailExtend.platform_order_subsidy_total
        from ${tableName}
        left join base_otype o on o.profile_id=d.profile_id and o.id=d.otype_id
        left join pl_eshop_sale_order od on od.profile_id=d.profile_id and od.id=d.eshop_order_id
        left join base_ptype bp on bp.profile_id=d.profile_id and d.ptype_id=bp.id
        <if test="submitNotNeedXcode == false">
            left join base_ptype_xcode bpx on bpx.profile_id=d.profile_id and bpx.sku_id=d.sku_id and bpx.unit_id=d.unit and
            bpx.defaulted=1 and bpx.ptype_id = d.ptype_id
        </if>
        left join base_ptype_fullbarcode bpf on bpf.profile_id=d.profile_id and bpf.ptype_id=d.ptype_id and
        bpf.sku_id=d.sku_id and bpf.unit_id=d.unit and bpf.defaulted=1
        left join base_ptype_pic pic on pic.profile_id = d.profile_id and d.ptype_id = pic.ptype_id and pic.rowindex = 1
        left join base_ptype_sku bps on bps.profile_id=d.profile_id and bps.id=d.sku_id
        left join base_ptype_unit unit on unit.profile_id=d.profile_id and unit.id=d.unit
        left join base_ptype_unit bsunit on bsunit.profile_id=d.profile_id and bsunit.ptype_id=d.ptype_id and
        bsunit.unit_code=1
        left join `base_brandtype` brand on bp.profile_id = brand.profile_id and bp.brand_id = brand.id
        left join `pl_eshop_sale_order_detail_purchase` dpurchase on d.profile_id = dpurchase.profile_id and d.id =
        dpurchase.eshop_order_detail_id and d.eshop_order_id = dpurchase.eshop_order_id
        left join `pl_eshop_sale_order_detail_distribution_buyer` distribution on d.profile_id = distribution.profile_id
        and d.eshop_order_id= distribution.eshop_order_id
        and d.id = distribution.eshop_order_detail_id
        left join `pl_eshop_product_mark` pepm on d.profile_id = pepm.profile_id and d.otype_id = pepm.eshop_id and
        d.platform_sku_id = pepm.platform_sku_id and d.platform_ptype_id = pepm.platform_num_id and
        d.platform_properties_name = pepm.platform_properties_name
        and pepm.mark_code=1009
        left join pl_eshop_sale_order_detail_live_broadcast liveBroadcast on liveBroadcast.profile_id=d.profile_id and
        liveBroadcast.eshop_order_id=d.eshop_order_id and
        liveBroadcast.detail_id=d.id
        left join pl_eshop_sale_order_detail_batch batch on batch.profile_id=d.profile_id and
        batch.eshop_order_detail_id=d.id and d.eshop_order_id = batch.eshop_order_id
        left join pl_eshop_sale_order_detail_extend detailExtend on d.profile_id = detailExtend.profile_id and
        d.otype_id = detailExtend.otype_id
        and d.id = detailExtend.eshop_order_detail_id and d.eshop_order_id = detailExtend.eshop_order_id
        left join pl_eshop_sale_order_detail_timing timing on d.profile_id = timing.profile_id and d.id =
        timing.eshop_order_detail_id and d.eshop_order_id = timing.eshop_order_id
        LEFT JOIN pl_eshop_platform_store_mapping smap ON smap.platform_store_stock_id = d.platform_stock_id AND
        smap.profile_id = d.profile_id AND smap.eshop_id = d.otype_id
        left join base_ktype bk on bk.profile_id=d.profile_id and bk.id=d.ktype_id
        where d.profile_id=#{profileId}
        <if test="eshopOrderId!=null">
            And d.eshop_order_id=#{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            And d.eshop_order_id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND d.mapping_state=0
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND d.mapping_state=1
        </if>
        <if test="waitAudit==true">
            AND d.mapping_state=1 AND d.local_refund_state!=4 AND d.platform_detail_trade_state!=2 and
            d.order_deliver_required=1 and d.ptype_id>0
        </if>
        <if test="waitSendAudit==true">
            AND d.mapping_state=1 AND d.platform_detail_trade_state!=2 and d.order_deliver_required=1
        </if>
        <if test="tradeOrderDetailIds!=null and tradeOrderDetailIds.size>0">
            And d.trade_order_detail_id IN
            <foreach collection="tradeOrderDetailIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size>0">
            And d.id IN
            <foreach collection="eshopOrderDetailIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        LIMIT #{pageIndex},#{pageSize}
    </select>

    <select id="queryUnRelationDetails" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail">
        select d.*,
        dpurchase.eshop_order_id as `purchase.eshop_order_id`,
        dpurchase.profile_id as `purchase.profile_id`,
        dpurchase.eshop_order_detail_id as `purchase.eshop_order_detail_id`,
        dpurchase.purchase_price as `purchase.purchase_price`,
        dpurchase.purchase_total as `purchase.purchase_total`,
        dpurchase.purchase_tax_rate as `purchase.purchase_tax_rate`,
        dpurchase.purchase_tax_total as `purchase.purchase_tax_total`,
        dpurchase.purchase_dised_price as `purchase.purchase_dised_price`,
        dpurchase.purchase_dised_total as `purchase.purchase_dised_total`,
        dpurchase.purchase_discount as `purchase.purchase_discount`,
        dpurchase.purchase_dised_taxed_price as `purchase.purchase_dised_taxed_price`,
        dpurchase.purchase_dised_taxed_total as `purchase.purchase_dised_taxed_total`,
        `distribution`.profile_id AS 'distribution.profile_id',
        `distribution`.eshop_order_id AS 'distribution.eshop_order_id',
        `distribution`.eshop_order_detail_id AS 'distribution.eshop_order_detail_id',
        `distribution`.eshop_id AS 'distribution.eshop_id',
        `distribution`.buyer_price AS 'distribution.buyer_price',
        `distribution`.buyer_total AS 'distribution.buyer_total',
        `distribution`.buyer_discount AS 'distribution.buyer_discount',
        `distribution`.buyer_dised_initial_price AS 'distribution.buyer_dised_initial_price',
        `distribution`.buyer_dised_initial_total AS 'distribution.buyer_dised_initial_total',
        `distribution`.buyer_ptype_preferential_total AS 'distribution.buyer_ptype_preferential_total',
        `distribution`.buyer_order_preferential_allot_total AS 'distribution.buyer_order_preferential_allot_total',
        IF( `o`.business_type != 203,d.dised_taxed_price, `distribution`.buyer_dised_taxed_price) AS
        'distribution.buyer_dised_taxed_price',
        IF( `o`.business_type != 203,d.dised_taxed_total, `distribution`.buyer_dised_taxed_total) AS
        'distribution.buyer_dised_taxed_total',
        `distribution`.buyer_tax_rate AS 'distribution.buyer_tax_rate',
        `distribution`.buyer_tax_total AS 'distribution.buyer_tax_total',
        `distribution`.buyer_dised_price AS 'distribution.buyer_dised_price',
        `distribution`.buyer_dised_total AS 'distribution.buyer_dised_total',
        `distribution`.distribution_buyer_trade_detail_id AS 'distribution.distribution_buyer_trade_detail_id',
        o.business_type AS businessType,
        o.btype_id AS btypeId,
        `liveBroadcast`.profile_id as 'liveBroadcast.profileId',
        `liveBroadcast`.eshop_id as 'liveBroadcast.eshopId',
        `liveBroadcast`.eshop_order_id as 'liveBroadcast.eshopOrderId',
        `liveBroadcast`.detail_id as 'liveBroadcast.detailId',
        `liveBroadcast`.platform_anchor_name as 'liveBroadcast.platformAnchorName',
        `liveBroadcast`.platform_anchor_id as 'liveBroadcast.platformAnchorId',
        `liveBroadcast`.platform_live_room_id as 'liveBroadcast.platformLiveRoomId',
        `liveBroadcast`.live_brodcast_session_id as 'liveBroadcast.liveBrodcastSessionId',
        `liveBroadcast`.create_time as 'liveBroadcast.createTime',
        `liveBroadcast`.update_time as 'liveBroadcast.updateTime',
        detailExtend.id as 'extend.id',
        detailExtend.profile_id as 'extend.profileId',
        detailExtend.otype_id as 'extend.otypeId',
        detailExtend.eshop_order_id as 'extend.eshopOrderId',
        detailExtend.eshop_order_detail_id as 'extend.detailId',
        detailExtend.mall_deduction_rate as 'extend.mallDeductionRate',
        detailExtend.mall_deduction_fee as 'extend.mallDeductionFee',
        detailExtend.anchor_order_preferential_total as 'extend.anchorOrderPreferentialTotal',
        detailExtend.anchor_ptype_preferential_total as 'extend.anchorPtypePreferentialTotal',
        detailExtend.platform_order_subsidy_total as 'extend.platformOrderSubsidyTotal',
        detailExtend.platform_ptype_subsidy_total as 'extend.platformPtypeSubsidyTotal',
        detailExtend.national_subsidy_total as 'extend.nationalSubsidyTotal',
        detailExtend.gift as 'extend.gift'
        from pl_eshop_sale_order_detail d
        left join pl_eshop_sale_order_detail_purchase dpurchase on d.profile_id = dpurchase.profile_id and d.id =
        dpurchase.eshop_order_detail_id
        left join `pl_eshop_sale_order_detail_distribution_buyer` distribution on d.profile_id = distribution.profile_id
        and d.id = distribution.eshop_order_detail_id
        left join pl_eshop_sale_order_detail_live_broadcast liveBroadcast on liveBroadcast.profile_id=d.profile_id and
        liveBroadcast.detail_id=d.id
        left join pl_eshop_sale_order o on d.profile_id = o.profile_id and d.eshop_order_id=o.id
        left join pl_eshop_sale_order_detail_extend detailExtend on d.profile_id = detailExtend.profile_id and
        d.otype_id = detailExtend.otype_id and d.id = detailExtend.eshop_order_detail_id
        where d.profile_id=#{profileId} and d.mapping_state=0
        <if test="eshopId!=null">
            and d.otype_id=#{eshopId}
        </if>
        <if test="platformPtypeId!=null and platformPtypeId.size() > 0 ">
            and d.platform_ptype_id in
            <foreach collection="platformPtypeId" close=")" open="(" separator="," item="num_id">
                #{num_id}
            </foreach>
        </if>
        <if test="platformProperties!=null and platformProperties.size() > 0 ">
            and d.platform_properties_name in
            <foreach collection="platformProperties" close=")" open="(" separator="," item="prop">
                #{prop}
            </foreach>
        </if>
        <if test="platformSkuInfo!=null and platformSkuInfo!=''">
            and (d.platform_ptype_name like CONCAT('%',#{platformSkuInfo},'%')
            or d.platform_ptype_xcode like CONCAT('%',#{platformSkuInfo},'%'))
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            and d.id in
            <foreach collection="eshopOrderDetailIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            and d.eshop_order_id in
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="queryComboRow" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleDetailCombo">
        select pl.*,
        o.platform_parent_order_id,
        ptype.fullname as ptypeName,
        ptype.id as ptypeId,
        ptype.shortname as ptypeShortName,
        xcode.xcode as xcode,
        ptype.barcode,
        ptype.weight_unit,
        ptype.weight as ptypeWeight,
        ptype.memo as ptypeMemo,
        ptype.usercode as ptypeCode,
        pic.pic_url as picUrl,
        unit.id as unit,
        cpurchase.eshop_order_id as `purchase.eshop_order_id`,
        cpurchase.profile_id as `purchase.profile_id`,
        cpurchase.eshop_order_combo_row_id as `purchase.eshop_order_combo_row_id`,
        cpurchase.purchase_price as `purchase.purchase_price`,
        cpurchase.purchase_total as `purchase.purchase_total`,
        cpurchase.purchase_tax_rate as `purchase.purchase_tax_rate`,
        cpurchase.purchase_tax_total as `purchase.purchase_tax_total`,
        cpurchase.purchase_dised_price as `purchase.purchase_dised_price`,
        cpurchase.purchase_dised_total as `purchase.purchase_dised_total`,
        cpurchase.purchase_discount as `purchase.purchase_discount`,
        cpurchase.purchase_dised_taxed_price as `purchase.purchase_dised_taxed_price`,
        cpurchase.purchase_dised_taxed_total as `purchase.purchase_dised_taxed_total`,
        `distribution`.profile_id AS 'distribution.profile_id',
        `distribution`.eshop_order_id AS 'distribution.eshop_order_id',
        `distribution`.eshop_order_combo_row_id AS 'distribution.eshop_order_combo_row_id',
        `distribution`.buyer_price AS 'distribution.buyer_price',
        `distribution`.buyer_total AS 'distribution.buyer_total',
        `distribution`.buyer_discount AS 'distribution.buyer_discount',
        `distribution`.buyer_dised_initial_price AS 'distribution.buyer_dised_initial_price',
        `distribution`.buyer_dised_initial_total AS 'distribution.buyer_dised_initial_total',
        `distribution`.buyer_ptype_preferential_total AS 'distribution.buyer_ptype_preferential_total',
        `distribution`.buyer_order_preferential_allot_total AS 'distribution.buyer_order_preferential_allot_total',
        IF( `o`.business_type != 203,pl.dised_taxed_price, `distribution`.buyer_dised_taxed_price) AS
        'distribution.buyer_dised_taxed_price',
        IF( `o`.business_type != 203,pl.dised_taxed_total, `distribution`.buyer_dised_taxed_total) AS
        'distribution.buyer_dised_taxed_total',
        `distribution`.buyer_tax_rate AS 'distribution.buyer_tax_rate',
        `distribution`.buyer_tax_total AS 'distribution.buyer_tax_total',
        `distribution`.buyer_dised_price AS 'distribution.buyer_dised_price',
        `distribution`.buyer_dised_total AS 'distribution.buyer_dised_total',
        `distribution`.distribution_buyer_trade_detail_id AS 'distribution.distribution_buyer_trade_detail_id',
        `liveBroadcast`.profile_id as 'liveBroadcast.profileId',
        `liveBroadcast`.eshop_id as 'liveBroadcast.eshopId',
        `liveBroadcast`.eshop_order_id as 'liveBroadcast.eshopOrderId',
        `liveBroadcast`.eshop_order_detail_combo_row_id as 'liveBroadcast.eshopOrderDetailComboRowId',
        `liveBroadcast`.platform_anchor_name as 'liveBroadcast.platformAnchorName',
        `liveBroadcast`.platform_anchor_id as 'liveBroadcast.platformAnchorId',
        `liveBroadcast`.platform_anchor_name as 'liveBroadcast.platformAnchorName',
        `liveBroadcast`.platform_live_room_id as 'liveBroadcast.platformLiveRoomId',
        `liveBroadcast`.live_brodcast_session_id as 'liveBroadcast.liveBrodcastSessionId',
        `liveBroadcast`.create_time as 'liveBroadcast.createTime',
        `liveBroadcast`.update_time as 'liveBroadcast.updateTime'
        from pl_eshop_sale_order_detail_combo pl
        left join pl_eshop_sale_order o on o.profile_id = pl.profile_id and o.id = pl.eshop_order_id
        left join base_ptype ptype on ptype.profile_id = pl.profile_id and pl.combo_id = ptype.id
        left join base_ptype_pic pic
        on pic.profile_id = pl.profile_id and pl.combo_id = pic.ptype_id and pic.rowindex = 1
        left join base_ptype_unit unit on unit.profile_id = pl.profile_id and unit.ptype_id = ptype.id
        left join base_ptype_xcode xcode on xcode.profile_id = pl.profile_id and xcode.ptype_id = ptype.id and
        xcode.unit_id = unit.id and xcode.info_type=1 and xcode.defaulted=1
        left join pl_eshop_sale_order_detail_combo_purchase cpurchase
        on cpurchase.profile_id = pl.profile_id and
        pl.eshop_order_detail_combo_row_id = cpurchase.eshop_order_combo_row_id
        and pl.eshop_order_id = cpurchase.eshop_order_id
        left join pl_eshop_sale_order_detail_combo_distribution_buyer distribution
        on distribution.profile_id = pl.profile_id and
        pl.eshop_order_detail_combo_row_id = distribution.eshop_order_combo_row_id
        and pl.eshop_order_id = distribution.eshop_order_id
        left join pl_eshop_sale_order_detail_combo_live_broadcast liveBroadcast
        on liveBroadcast.profile_id = pl.profile_id and
        liveBroadcast.eshop_order_detail_combo_row_id = pl.eshop_order_detail_combo_row_id
        and pl.eshop_order_id = liveBroadcast.eshop_order_id
        where pl.profile_id = #{profileId}
        <if test="eshopOrderId!=null">
            and pl.eshop_order_id = #{eshopOrderId}
        </if>
        <if test="tradeOrderDetailIds!=null and tradeOrderDetailIds.size()>0">
            and pl.trade_order_detail_id in
            <foreach collection="tradeOrderDetailIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            And pl.eshop_order_id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size>0">
            And pl.eshop_order_detail_combo_row_id IN
            <foreach collection="eshopOrderDetailIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>

    <insert id="insertSaleOrder">
        INSERT INTO pl_eshop_sale_order (id, `trade_order_id`, otype_id, profile_id, `create_type`, `creator_id`,
                                         `platform_trade_state`, `local_trade_state`, `local_refund_process_state`,
                                         `seller_flag`, `platform_store_id`, `platform_store_code`, `business_type`,
                                         `order_source_type`, `deliver_type`, `pay_time_type`, `trade_create_time`,
                                         `trade_modified_time`, `trade_finish_time`, `trade_pay_time`, `create_time`,
                                         `modified_time`, `local_freight_name`, local_freight_code,
                                         `local_freight_bill_no`,
                                         `platform_freight_name`, `platform_freight_code`,
                                         `customer_expected_freight_name`, `customer_expected_freight_code`,
                                         `trade_total`, `buyer_trade_total`, `buyer_unpaid_total`, `dised_taxed_total`,
                                         `ptype_preferential_total`, `tax_total`, `total`,
                                         `order_buyer_freight_fee`, `ptype_service_fee`, `mapping_state`,
                                         `order_deliver_required`, `buyer_id`, `btype_id`, `ktype_id`, `seller_memo`,
                                         `buyer_message`, `remark`, pay_no, etype_id,
                                         dtype_id, process_state, unique_mark,
                                         local_refund_state, order_sale_type, self_delivery_mode,
                                         salesman, summary, platform_ptype_preferential_total,
                                         order_preferential_allot_total, platform_order_preferential_total,
                                         ptype_commission_total, distribution_dised_taxed_total,
                                         buyer_paid_total,
                                         platform_stock_id, platform_stock_code, pay_btype_id, commission_btype_id,
                                         distribution_tax_total, receive_address_type, receive_address_id,
                                         platform_distributor_name,
                                         mode_of_payment, merchant_payment_account, payway_id, platform_parent_order_id,
                                         re_send_state, sender_id, deliver_process_type, platform_distributor_id)
            value
            (#{id}, #{tradeOrderId}, #{otypeId}, #{profileId}, #{createType}, #{creatorId}, #{platformTradeState},
             #{localTradeState}, #{localRefundProcessState}, #{sellerFlag}, #{platformStoreId}, #{platformStoreCode},
             #{businessType}, #{orderSourceType}, #{deliverType}, #{payTimeType}, #{tradeCreateTime},
             #{tradeModifiedTime},
             #{tradeFinishTime}, #{tradePayTime}, #{createTime}, #{modifiedTime}, #{localFreightName},
             #{localFreightCode},
             #{localFreightBillNo}, #{platformFreightName}, #{platformFreightCode}, #{customerExpectedFreightName},
             #{customerExpectedFreightCode}, #{tradeTotal}, #{buyerTradeTotal}, #{buyerUnpaidTotal}, #{disedTaxedTotal},
             #{ptypePreferentialTotal}, #{taxTotal}, #{total}, #{orderBuyerFreightFee},
             #{ptypeServiceFee}, #{mappingState}, #{orderDeliverRequired}, #{buyerId}, #{btypeId}, #{ktypeId},
             #{sellerMemo},
             #{buyerMessage}, #{remark}, #{payNo}, #{etypeId}, #{dtypeId}, #{processState},
             #{uniqueMark}, #{localRefundState}, #{orderSaleType},
             #{selfDeliveryMode}, #{salesman}, #{summary}, #{platformPtypePreferentialTotal},
             #{orderPreferentialAllotTotal}, #{platformOrderPreferentialTotal},
             #{ptypeCommissionTotal}, #{distributionDisedTaxedTotal}, #{buyerPaidTotal},
             #{platformStockId}, #{platformStockCode}, #{payBtypeId}, #{commissionBtypeId}, #{distributionTaxTotal},
             #{receiveAddressType}, #{receiveAddressId}, #{platformDistributorName},
             #{modeOfPayment}, #{merchantPaymentAccount}, #{paywayId}, #{platformParentOrderId},
             #{reSendState}, #{senderId}, #{deliverProcessType}, #{platformDistributorId})
        ON DUPLICATE KEY
            UPDATE ktype_id=#{ktypeId},
                   otype_id=#{otypeId},
                   btype_id=#{btypeId},
                   trade_order_id=#{tradeOrderId},
                   buyer_id=#{buyerId},
                   etype_id=#{etypeId},
                   dtype_id=#{dtypeId},
                   platform_trade_state=#{platformTradeState},
                   local_trade_state=#{localTradeState},
                   local_refund_process_state=#{localRefundProcessState},
                   seller_flag=#{sellerFlag},
                   platform_store_id=#{platformStoreId},
                   platform_store_code=#{platformStoreCode},
                   business_type=#{businessType},
                   order_source_type=#{orderSourceType},
                   deliver_type=#{deliverType},
                   pay_time_type=#{payTimeType},
                   trade_modified_time=#{tradeModifiedTime},
                   trade_finish_time=#{tradeFinishTime},
                   trade_pay_time=#{tradePayTime},
                   modified_time=#{modifiedTime},
                   local_freight_code=#{localFreightCode},
                   local_freight_bill_no=#{localFreightBillNo},
                   platform_freight_name=#{platformFreightName},
                   platform_freight_code=#{platformFreightCode},
                   customer_expected_freight_name=#{customerExpectedFreightName},
                   customer_expected_freight_code=#{customerExpectedFreightCode},
                   trade_total=#{tradeTotal},
                   buyer_trade_total=#{buyerTradeTotal},
                   buyer_unpaid_total=#{buyerUnpaidTotal},
                   dised_taxed_total=#{disedTaxedTotal},
                   ptype_preferential_total=#{ptypePreferentialTotal},
                   tax_total=#{taxTotal},
                   total=#{total},
                   order_buyer_freight_fee=#{orderBuyerFreightFee},
                   ptype_service_fee=#{ptypeServiceFee},
                   mapping_state=#{mappingState},
                   order_deliver_required=#{orderDeliverRequired},
                   seller_memo=#{sellerMemo},
                   seller_flag=#{sellerFlag},
                   buyer_message=#{buyerMessage},
                   pay_no=#{payNo},
                   local_refund_state=#{localRefundState},
                   order_sale_type=#{orderSaleType},
                   self_delivery_mode=#{selfDeliveryMode},
                   salesman=#{salesman},
                   summary=#{summary},
                   platform_ptype_preferential_total=#{platformPtypePreferentialTotal},
                   order_preferential_allot_total=#{orderPreferentialAllotTotal},
                   receive_address_type=#{receiveAddressType},
                   receive_address_id=#{receiveAddressId},
                   platform_distributor_name=#{platformDistributorName},
                   mode_of_payment=#{modeOfPayment},
                   merchant_payment_account=#{merchantPaymentAccount},
                   payway_id=#{paywayId},
                   platform_parent_order_id=#{platformParentOrderId},
                   re_send_state=#{reSendState},
                   sender_id=#{senderId},
                   deliver_process_type=#{deliverProcessType},
                   platform_distributor_id=#{platformDistributorId}
    </insert>

    <insert id="batchInsertSaleOrder">
        INSERT INTO pl_eshop_sale_order (id, `trade_order_id`, otype_id, profile_id, `create_type`, `creator_id`,
                                         `platform_trade_state`, `local_trade_state`, `local_refund_process_state`,
                                         `seller_flag`, `platform_store_id`, `platform_store_code`, `business_type`,
                                         `order_source_type`, `deliver_type`, `pay_time_type`, `trade_create_time`,
                                         `trade_modified_time`, `trade_finish_time`, `trade_pay_time`, `create_time`,
                                         `modified_time`, `local_freight_name`, local_freight_code,
                                         `local_freight_bill_no`,
                                         `platform_freight_name`, `platform_freight_code`,
                                         `customer_expected_freight_name`, `customer_expected_freight_code`,
                                         `trade_total`, `buyer_trade_total`, `buyer_unpaid_total`, `dised_taxed_total`,
                                         `ptype_preferential_total`, `tax_total`, `total`,
                                         `order_buyer_freight_fee`, `ptype_service_fee`, `mapping_state`,
                                         `order_deliver_required`, `buyer_id`, `btype_id`, `ktype_id`, `seller_memo`,
                                         `buyer_message`, `remark`, pay_no, etype_id,
                                         dtype_id, process_state, unique_mark,
                                         local_refund_state, order_sale_type, self_delivery_mode,
                                         salesman, summary, platform_ptype_preferential_total,
                                         order_preferential_allot_total, platform_order_preferential_total,
                                         ptype_commission_total, distribution_dised_taxed_total,
                                         buyer_paid_total,
                                         platform_stock_id, platform_stock_code, pay_btype_id, commission_btype_id,
                                         distribution_tax_total, receive_address_type, receive_address_id,
                                         platform_distributor_name,
                                         mode_of_payment, merchant_payment_account, payway_id, platform_parent_order_id,
                                         re_send_state, sender_id, deliver_process_type, platform_distributor_id)
        VALUES
        <foreach collection="list" item="order" separator=",">
            (#{order.id}, #{order.tradeOrderId}, #{order.otypeId}, #{order.profileId}, #{order.createType}, #{order.creatorId}, #{order.platformTradeState},
             #{order.localTradeState}, #{order.localRefundProcessState}, #{order.sellerFlag}, #{order.platformStoreId}, #{order.platformStoreCode},
             #{order.businessType}, #{order.orderSourceType}, #{order.deliverType}, #{order.payTimeType}, #{order.tradeCreateTime},
             #{order.tradeModifiedTime},
             #{order.tradeFinishTime}, #{order.tradePayTime}, #{order.createTime}, #{order.modifiedTime}, #{order.localFreightName},
             #{order.localFreightCode},
             #{order.localFreightBillNo}, #{order.platformFreightName}, #{order.platformFreightCode}, #{order.customerExpectedFreightName},
             #{order.customerExpectedFreightCode}, #{order.tradeTotal}, #{order.buyerTradeTotal}, #{order.buyerUnpaidTotal}, #{order.disedTaxedTotal},
             #{order.ptypePreferentialTotal}, #{order.taxTotal}, #{order.total}, #{order.orderBuyerFreightFee},
             #{order.ptypeServiceFee}, #{order.mappingState}, #{order.orderDeliverRequired}, #{order.buyerId}, #{order.btypeId}, #{order.ktypeId},
             #{order.sellerMemo},
             #{order.buyerMessage}, #{order.remark}, #{order.payNo}, #{order.etypeId}, #{order.dtypeId}, #{order.processState},
             #{order.uniqueMark}, #{order.localRefundState}, #{order.orderSaleType},
             #{order.selfDeliveryMode}, #{order.salesman}, #{order.summary}, #{order.platformPtypePreferentialTotal},
             #{order.orderPreferentialAllotTotal}, #{order.platformOrderPreferentialTotal},
             #{order.ptypeCommissionTotal}, #{order.distributionDisedTaxedTotal}, #{order.buyerPaidTotal},
             #{order.platformStockId}, #{order.platformStockCode}, #{order.payBtypeId}, #{order.commissionBtypeId}, #{order.distributionTaxTotal},
             #{order.receiveAddressType}, #{order.receiveAddressId}, #{order.platformDistributorName},
             #{order.modeOfPayment}, #{order.merchantPaymentAccount}, #{order.paywayId}, #{order.platformParentOrderId},
             #{order.reSendState}, #{order.senderId}, #{order.deliverProcessType}, #{order.platformDistributorId})
        </foreach>
        ON DUPLICATE KEY
            UPDATE ktype_id=VALUES(ktype_id),
                   otype_id=VALUES(otype_id),
                   btype_id=VALUES(btype_id),
                   trade_order_id=VALUES(trade_order_id),
                   buyer_id=VALUES(buyer_id),
                   etype_id=VALUES(etype_id),
                   dtype_id=VALUES(dtype_id),
                   platform_trade_state=VALUES(platform_trade_state),
                   local_trade_state=VALUES(local_trade_state),
                   local_refund_process_state=VALUES(local_refund_process_state),
                   seller_flag=VALUES(seller_flag),
                   platform_store_id=VALUES(platform_store_id),
                   platform_store_code=VALUES(platform_store_code),
                   business_type=VALUES(business_type),
                   order_source_type=VALUES(order_source_type),
                   deliver_type=VALUES(deliver_type),
                   pay_time_type=VALUES(pay_time_type),
                   trade_modified_time=VALUES(trade_modified_time),
                   trade_finish_time=VALUES(trade_finish_time),
                   trade_pay_time=VALUES(trade_pay_time),
                   modified_time=VALUES(modified_time),
                   local_freight_code=VALUES(local_freight_code),
                   local_freight_bill_no=VALUES(local_freight_bill_no),
                   platform_freight_name=VALUES(platform_freight_name),
                   platform_freight_code=VALUES(platform_freight_code),
                   customer_expected_freight_name=VALUES(customer_expected_freight_name),
                   customer_expected_freight_code=VALUES(customer_expected_freight_code),
                   trade_total=VALUES(trade_total),
                   buyer_trade_total=VALUES(buyer_trade_total),
                   buyer_unpaid_total=VALUES(buyer_unpaid_total),
                   dised_taxed_total=VALUES(dised_taxed_total),
                   ptype_preferential_total=VALUES(ptype_preferential_total),
                   tax_total=VALUES(tax_total),
                   total=VALUES(total),
                   order_buyer_freight_fee=VALUES(order_buyer_freight_fee),
                   ptype_service_fee=VALUES(ptype_service_fee),
                   mapping_state=VALUES(mapping_state),
                   order_deliver_required=VALUES(order_deliver_required),
                   seller_memo=VALUES(seller_memo),
                   seller_flag=VALUES(seller_flag),
                   buyer_message=VALUES(buyer_message),
                   pay_no=VALUES(pay_no),
                   local_refund_state=VALUES(local_refund_state),
                   order_sale_type=VALUES(order_sale_type),
                   self_delivery_mode=VALUES(self_delivery_mode),
                   salesman=VALUES(salesman),
                   summary=VALUES(summary),
                   platform_ptype_preferential_total=VALUES(platform_ptype_preferential_total),
                   order_preferential_allot_total=VALUES(order_preferential_allot_total),
                   receive_address_type=VALUES(receive_address_type),
                   receive_address_id=VALUES(receive_address_id),
                   platform_distributor_name=VALUES(platform_distributor_name),
                   mode_of_payment=VALUES(mode_of_payment),
                   merchant_payment_account=VALUES(merchant_payment_account),
                   payway_id=VALUES(payway_id),
                   platform_parent_order_id=VALUES(platform_parent_order_id),
                   re_send_state=VALUES(re_send_state),
                   sender_id=VALUES(sender_id),
                   deliver_process_type=VALUES(deliver_process_type),
                   platform_distributor_id=VALUES(platform_distributor_id)
    </insert>

    <insert id="insertSaleOrderBatch">
        INSERT INTO pl_eshop_sale_order (id, `trade_order_id`, otype_id, profile_id, `create_type`, `creator_id`,
        `platform_trade_state`, `local_trade_state`, `local_refund_process_state`,
        `seller_flag`, `platform_store_id`, `platform_store_code`, `business_type`,
        `order_source_type`, `deliver_type`, `pay_time_type`, `trade_create_time`,
        `trade_modified_time`, `trade_finish_time`, `trade_pay_time`, `create_time`,
        `modified_time`, local_freight_name, local_freight_code, `local_freight_bill_no`,
        `platform_freight_name`, `platform_freight_code`,
        `customer_expected_freight_name`, `customer_expected_freight_code`,
        `trade_total`, `buyer_trade_total`, `buyer_unpaid_total`, `dised_taxed_total`,
        `ptype_preferential_total`, `tax_total`, `total`,
        `order_buyer_freight_fee`, `ptype_service_fee`, `mapping_state`,
        `order_deliver_required`, `buyer_id`, `btype_id`, `ktype_id`, `seller_memo`,
        `buyer_message`, `remark`, pay_no, etype_id,
        dtype_id, process_state,unique_mark,
        local_refund_state, order_sale_type, self_delivery_mode,
        salesman, summary, platform_ptype_preferential_total, order_preferential_allot_total,
        ptype_commission_total, distribution_dised_taxed_total,
        buyer_paid_total,
        platform_stock_id,platform_stock_code,pay_btype_id,commission_btype_id,distribution_tax_total,
        receive_address_type, receive_address_id
        ,mode_of_payment,merchant_payment_account,payway_id)
        values
        <foreach collection="list" item="order" separator=",">
            (#{order.id}, #{order.tradeOrderId}, #{order.otypeId}, #{order.profileId}, #{order.createType},
            #{order.creatorId}, #{order.platformTradeStatus},
            #{order.customTradeStatus}, #{order.platformRefundStatus}, #{order.sellerFlag}, #{order.platformStoreId},
            #{order.platformStoreCode},
            #{order.businessType}, #{order.orderType}, #{order.deliverType}, #{order.payType}, #{order.tradeCreateTime},
            #{order.tradeModifiedTime},
            #{order.tradeFinishTime}, #{order.tradePayTime}, #{order.createTime}, #{order.modifiedTime},
            #{order.localFreightName}, #{order.localFreightCode},
            #{order.localFreightBillNo}, #{order.platformFreightName}, #{order.platformFreightCode},
            #{order.customerExpectedFreightName},
            #{order.customerExpectedFreightCode}, #{order.tradeTotal}, #{order.buyerTradeTotal},
            #{order.buyerUnpaidTotal}, #{order.disedTaxedTotal},
            #{order.ptypeTreferentialTotal}, #{order.taxTotal}, #{order.total},
            #{order.orderBuyerFreightFee},
            #{order.ptypeServiceFee}, #{order.mappingState}, #{order.deliverRequired}, #{order.buyerId},
            #{order.btypeId},
            #{order.ktypeId}, #{order.sellerMemo},
            #{order.buyerMessage}, #{order.remark}, #{order.payNo},
            #{order.etypeId}, #{order.dtypeId}, #{order.processState},
            #{order.uniqueMark}, #{order.returnState}, #{order.tradeType},
            #{order.selfDeliveryMode}, #{order.salesman}, #{order.summary}, #{order.platformPtypePreferentialTotal},
            #{order.orderPreferentialAllotTotal},
            #{order.ptypeCommissionTotal}, #{order.distributionBalanceTaxedTotal}, #{order.buyerPaidTotal},
            #{platformStockId}, #{platformStockCode}, #{payBtypeId},#{commissionBtypeId}, #{distributionTaxTotal}
            , #{receiveAddressType}, #{receiveAddressId},
            #{order.modeOfPayment}, #{order.merchantPaymentAccount}, #{order.paywayId})
        </foreach>
    </insert>

    <insert id="insertSaleOrderExtend">
        INSERT INTO pl_eshop_sale_order_extend (`eshop_order_id`, `export_count`, `order_number`, `otype_id`,
                                                `profile_id`,
                                                `create_time`, `update_time`, `deliver_send_state`,
                                                `Installation_service_provider`, `collect_time`, `collect_customer`,
                                                `gather_status`, `payment_mode`, `mark_info`,
                                                `hold_time`, `platform_required`, `platform_json`, `group_header_name`,
                                                `purchase_total`, `supplier_id`, `platform_send_time`, `advance_total`,
                                                `pickup_code`, `platform_dispatcher_name`, `platform_dispather_mobile`,
                                                `logistics_status`, `confirm_status`,
                                                `trade_summary_md5`, `buyer_unique_mark`, `real_buyer_id`,
                                                `pick_up_address_id`, `platform_qc_result`, `platform_identify_result`,
                                                `flow_channel`, `mall_deduction_fee`, `seller_flag_memo`,
                                                `group_title`, `group_no`, `take_goods_code`, `platform_quality_org_id`,
                                                `platform_quality_org_name`, `platform_quality_warehouse_code`,
                                                `platform_quality_warehouse_name`, `platform_hash_order_main`,
                                                `platform_hash_order_detail`, `platform_hash_mark_list`,
                                                `is_draft`, `anchor_order_preferential_total`,
                                                `platform_order_subsidy_total`, `sale_period_num`, `current_period_num`)
            value
            (#{eshopOrderId}, #{exportCount}, #{orderNumber}, #{otypeId}, #{profileId}, #{createTime}, #{updateTime},
             #{deliverSendState}, #{installationServiceProvider}, #{collectTime}, #{collectCustomer},
             #{gatherStatus}, #{paymentMode}, #{markInfo}, #{holdTime}, #{platformRequired}, #{platformJson},
             #{groupHeaderName}, #{purchaseTotal}, #{supplierId}, #{platformSendTime}, #{advanceTotal},
             #{pickupCode}, #{platformDispatcherName}, #{platformDispatherMobile}, #{logisticsStatus}, #{confirmStatus},
             #{tradeSummaryMd5}, #{buyerUniqueMark}, #{realBuyerId}, #{pickUpAddressId}, #{platformQcResult},
             #{platformIdentifyResult}, #{flowChannel}, #{mallDeductionFee}, #{sellerFlagMemo},
             #{groupTitle}, #{groupNo}, #{takeGoodsCode}, #{platformQualityOrgId}, #{platformQualityOrgName},
             #{platformQualityWarehouseCode}, #{platformQualityWarehouseName}, #{platformHashOrderMain},
             #{platformHashOrderDetail}, #{platformHashMarkList},
             #{isDraft}, #{anchorOrderPreferentialTotal}, #{platformOrderSubsidyTotal}, #{salePeriodNum},
             #{currentPeriodNum})
    </insert>

    <insert id="batchInsertSaleOrderExtend">
        INSERT INTO pl_eshop_sale_order_extend (`eshop_order_id`, `export_count`, `order_number`, `otype_id`,
                                                `profile_id`,
                                                `create_time`, `update_time`, `deliver_send_state`,
                                                `Installation_service_provider`, `collect_time`, `collect_customer`,
                                                `gather_status`, `payment_mode`, `mark_info`,
                                                `hold_time`, `platform_required`, `platform_json`, `group_header_name`,
                                                `purchase_total`, `supplier_id`, `platform_send_time`, `advance_total`,
                                                `pickup_code`, `platform_dispatcher_name`, `platform_dispather_mobile`,
                                                `logistics_status`, `confirm_status`,
                                                `trade_summary_md5`, `buyer_unique_mark`, `real_buyer_id`,
                                                `pick_up_address_id`, `platform_qc_result`, `platform_identify_result`,
                                                `flow_channel`, `mall_deduction_fee`, `seller_flag_memo`,
                                                `group_title`, `group_no`, `take_goods_code`, `platform_quality_org_id`,
                                                `platform_quality_org_name`, `platform_quality_warehouse_code`,
                                                `platform_quality_warehouse_name`, `platform_hash_order_main`,
                                                `platform_hash_order_detail`, `platform_hash_mark_list`,
                                                `is_draft`, `anchor_order_preferential_total`,
                                                `platform_order_subsidy_total`, `sale_period_num`, `current_period_num`, `national_subsidy_total`)
            values
        <foreach collection="list" item="saleOrderExtend" separator=",">
            (#{saleOrderExtend.eshopOrderId}, #{saleOrderExtend.exportCount}, #{saleOrderExtend.orderNumber}, #{saleOrderExtend.otypeId}, #{saleOrderExtend.profileId}, #{saleOrderExtend.createTime}, #{saleOrderExtend.updateTime},
             #{saleOrderExtend.deliverSendState}, #{saleOrderExtend.installationServiceProvider}, #{saleOrderExtend.collectTime}, #{saleOrderExtend.collectCustomer},
             #{saleOrderExtend.gatherStatus}, #{saleOrderExtend.paymentMode}, #{saleOrderExtend.markInfo}, #{saleOrderExtend.holdTime}, #{saleOrderExtend.platformRequired}, #{saleOrderExtend.platformJson},
             #{saleOrderExtend.groupHeaderName}, #{saleOrderExtend.purchaseTotal}, #{saleOrderExtend.supplierId}, #{saleOrderExtend.platformSendTime}, #{saleOrderExtend.advanceTotal},
             #{saleOrderExtend.pickupCode}, #{saleOrderExtend.platformDispatcherName}, #{saleOrderExtend.platformDispatherMobile}, #{saleOrderExtend.logisticsStatus}, #{saleOrderExtend.confirmStatus},
             #{saleOrderExtend.tradeSummaryMd5}, #{saleOrderExtend.buyerUniqueMark}, #{saleOrderExtend.realBuyerId}, #{saleOrderExtend.pickUpAddressId}, #{saleOrderExtend.platformQcResult},
             #{saleOrderExtend.platformIdentifyResult}, #{saleOrderExtend.flowChannel}, #{saleOrderExtend.mallDeductionFee}, #{saleOrderExtend.sellerFlagMemo},
             #{saleOrderExtend.groupTitle}, #{saleOrderExtend.groupNo}, #{saleOrderExtend.takeGoodsCode}, #{saleOrderExtend.platformQualityOrgId}, #{saleOrderExtend.platformQualityOrgName},
             #{saleOrderExtend.platformQualityWarehouseCode}, #{saleOrderExtend.platformQualityWarehouseName}, #{saleOrderExtend.platformHashOrderMain},
             #{saleOrderExtend.platformHashOrderDetail}, #{saleOrderExtend.platformHashMarkList},
             #{saleOrderExtend.isDraft}, #{saleOrderExtend.anchorOrderPreferentialTotal}, #{saleOrderExtend.platformOrderSubsidyTotal}, #{saleOrderExtend.salePeriodNum},
             #{saleOrderExtend.currentPeriodNum},#{saleOrderExtend.nationalSubsidyTotal})
        </foreach>
    </insert>

    <insert id="insertSaleOrderDistributionBuyer">
        INSERT INTO pl_eshop_sale_order_distribution_buyer (`profile_id`, `distribution_buyer_trade_id`,
                                                            `eshop_order_id`, `create_time`, `update_time`)
            value
            (#{profileId}, #{distributionBuyerTradeId}, #{eshopOrderId}, #{createTime}, #{updateTime})
    </insert>

    <insert id="batchInsertSaleOrderDistributionBuyer">
        INSERT INTO pl_eshop_sale_order_distribution_buyer (`profile_id`, `distribution_buyer_trade_id`,
                                                            `eshop_order_id`, `create_time`, `update_time`)
            values
        <foreach collection="list" item="distributionBuyer" separator=",">
            (#{distributionBuyer.profileId}, #{distributionBuyer.distributionBuyerTradeId}, #{distributionBuyer.eshopOrderId}, #{distributionBuyer.createTime}, #{distributionBuyer.updateTime})
        </foreach>
    </insert>

    <insert id="insertSaleOrderExtendBatch">
        INSERT INTO pl_eshop_sale_order_extend (`eshop_order_id`, `export_count`, `order_number`, `otype_id`,
        `profile_id`,
        `create_time`, `update_time`, `deliver_send_state`,
        `Installation_service_provider`, `collect_time`, `collect_customer`,
        `gather_status`, `payment_mode`, `mark_info`,
        `hold_time`, `platform_required`, `platform_json`,`group_header_name`,`mall_deduction_fee`)
        values
        <foreach collection="list" item="order" separator=",">
            (#{order.extend.eshopOrderId}, #{order.extend.exportCount}, #{order.extend.orderNumber},
            #{order.extend.otypeId},
            #{order.extend.profileId}, #{order.extend.createTime}, #{order.extend.updateTime},
            #{order.extend.deliverSendState}, #{order.extend.installationServiceProvider}, #{order.extend.collectTime},
            #{order.extend.collectCustomer},#{order.extend.gatherStatus}, #{order.extend.paymentMode},
            #{order.extend.markInfo},#{order.extend.holdTime}, #{order.extend.platformRequired},
            #{order.extend.platformJson},#{order.extend.groupHeaderName},#{order.extend.mallDeductionFee})
        </foreach>
    </insert>

    <insert id="insertSaleOrderDetail">
        INSERT INTO pl_eshop_sale_order_detail
        (`id`, `eshop_order_id`, `otype_id`, `profile_id`, `trade_order_detail_id`, `platform_ptype_id`,
         `platform_sku_id`,
         `platform_ptype_name`, `platform_properties_name`, `platform_ptype_xcode`, `platform_ptype_pic_url`,
         `platform_store_id`,
         `platform_store_code`, `trade_total`, `dised_taxed_total`,
         `refund_total`, `tax_total`, `total`, `trade_price`, `dised_taxed_price`,
         `price`, `qty`, `sub_qty`,
         unit_qty, `refund_qty`, `ptype_id`, `sku_id`, `unit`,
         `combo_row_id`, `local_mapping_mark`, `mapping_state`, `deliver_required`, `seller_memo`, `buyer_message`,
         `ktype_id`, `attribute`,
         ptype_service_fee, stock_sync_rule_id, local_refund_process_state, platform_detail_trade_state, tax_rate,
         discount, remark,
         local_refund_state, deleted, order_sale_type, ptype_preferential_total,
         platform_ptype_preferential_total, order_preferential_allot_total, platform_order_preferential_total,
         ptype_commission_total,
         platform_stock_id, platform_stock_code, dised_initial_price, dised_initial_total, dised_price, dised_total,
         process_state, combo_share_scale, re_send_state, batchno, produce_date, expire_date, batch_price,
         display_custom_info, `platform_qc_result`, `platform_qc_result_desc`, `platform_identify_result`,
         `platform_identify_result_desc`, `verify_code`, `flow_channel`)
        values (#{id}, #{eshopOrderId}, #{otypeId}, #{profileId}, #{tradeOrderDetailId}, #{platformPtypeId},
                #{platformSkuId},
                #{platformPtypeName}, #{platformPropertiesName}, #{platformPtypeXcode}, #{platformPtypePicUrl},
                #{platformStoreId}, #{platformStoreCode}, #{tradeTotal}, #{disedTaxedTotal},
                #{refundTotal}, #{taxTotal}, #{total}, #{tradePrice}, #{disedTaxedPrice},
                #{price},
                #{qty}, #{subQty}, #{unitQty}, #{refundQty}, #{ptypeId},
                #{skuId}, #{unit}, #{comboRowId}, #{localMappingMark}, #{mappingState}, #{deliverRequired},
                #{sellerMemo}, #{buyerMessage},
                #{ktypeId}, #{attribute}, #{ptypeServiceFee}, #{stockSyncRuleId},
                #{localRefundProcessState}, #{platformDetailTradeState}, #{taxRate}, #{discount}, #{remark},
                #{localRefundState}, #{deleted}, #{orderSaleType}, #{ptypePreferentialTotal},
                #{platformPtypePreferentialTotal}, #{orderPreferentialAllotTotal}, #{platformOrderPreferentialTotal},
                #{ptypeCommissionTotal},
                #{platformStockId}, #{platformStockCode}, #{disedInitialPrice}, #{disedInitialTotal}, #{disedPrice},
                #{disedTotal}, #{processState}, #{comboShareScale}, #{reSendState}, #{batchno}, #{produceDate},
                #{expireDate}, #{batchPrice}, #{displayCustomInfo}, #{platformQcResult}, #{platformQcResultDesc},
                #{platformIdentifyResult}, #{platformIdentifyResultDesc}, #{verifyCode}, #{flowChannel})
        ON DUPLICATE KEY
            UPDATE `eshop_order_id`=#{eshopOrderId},
                   `otype_id`=#{otypeId},
                   `profile_id`=#{profileId},
                   `trade_order_detail_id`=#{tradeOrderDetailId},
                   `platform_ptype_id` = #{platformPtypeId},
                   `platform_sku_id` = #{platformSkuId},
                   `platform_ptype_name` = #{platformPtypeName},
                   `platform_properties_name` = #{platformPropertiesName},
                   `platform_ptype_xcode` = #{platformPtypeXcode},
                   `platform_ptype_pic_url` = #{platformPtypePicUrl},
                   `platform_store_id` = #{platformStoreId},
                   `platform_store_code` = #{platformStoreCode},
                   `display_custom_info` = #{displayCustomInfo},
                   `platform_qc_result` = #{platformQcResult},
                   `platform_qc_result_desc` = #{platformQcResultDesc},
                   `platform_identify_result` = #{platformIdentifyResult},
                   `platform_identify_result_desc` = #{platformIdentifyResultDesc},
                   `verify_code` = #{verifyCode},
                   `flow_channel` = #{flowChannel}
    </insert>

    <insert id="batchInsertSaleOrderDetail">
        INSERT INTO pl_eshop_sale_order_detail
        (`id`, `eshop_order_id`, `otype_id`, `profile_id`, `trade_order_detail_id`, `platform_ptype_id`,
        `platform_sku_id`,
        `platform_ptype_name`, `platform_properties_name`, `platform_ptype_xcode`, `platform_ptype_pic_url`,
        `platform_store_id`,
        `platform_store_code`, `trade_total`, `dised_taxed_total`,
        `refund_total`, `tax_total`, `total`, `trade_price`, `dised_taxed_price`,
        `price`, `qty`, `sub_qty`,
        unit_qty, `refund_qty`, `ptype_id`, `sku_id`, `unit`,
        `combo_row_id`, `local_mapping_mark`, `mapping_state`, `deliver_required`, `seller_memo`, `buyer_message`,
        `ktype_id`, `attribute`,
        ptype_service_fee, stock_sync_rule_id, local_refund_process_state, platform_detail_trade_state, tax_rate,
        discount, remark,
        local_refund_state, deleted, order_sale_type, ptype_preferential_total,
        platform_ptype_preferential_total, order_preferential_allot_total, platform_order_preferential_total,
        ptype_commission_total,
        platform_stock_id,platform_stock_code,dised_initial_price,dised_initial_total,dised_price,dised_total,process_state,
        combo_share_scale,`display_custom_info`,`verify_code`,`flow_channel`,
        platform_qc_result,`platform_qc_result_desc`,`platform_identify_result`,`platform_identify_result_desc`)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id}, #{item.eshopOrderId}, #{item.otypeId}, #{item.profileId}, #{item.tradeOrderDetailId},
            #{item.platformPtypeId},
            #{item.platformSkuId},
            #{item.platformPtypeName}, #{item.platformPropertiesName}, #{item.platformPtypeXcode},
            #{item.platformPtypePicUrl},
            #{item.platformStoreId}, #{item.platformStoreCode}, #{item.tradeTotal}, #{item.disedTaxedTotal},
            #{item.refundTotal}, #{item.taxTotal}, #{item.total}, #{item.tradePrice},
            #{item.disedTaxedPrice},
            #{item.price},
            #{item.qty}, #{item.subQty}, #{item.unitQty}, #{item.refundQty},
            #{item.ptypeId},
            #{item.skuId}, #{item.unit}, #{item.comboRowId}, #{item.localMappingMark}, #{item.mappingState},
            #{item.deliverRequired},
            #{item.sellerMemo}, #{item.buyerMessage},
            #{item.ktypeId}, #{item.attribute}, #{item.ptypeServiceFee},
            #{item.stockSyncRuleId},
            #{item.localRefundProcessState}, #{item.platformDetailTradeState}, #{item.taxRate}, #{item.discount},
            #{item.remark},
            #{item.localRefundState}, #{item.deleted}, #{item.orderSaleType}, #{item.ptypePreferentialTotal},
            #{item.platformPtypePreferentialTotal}, #{item.orderPreferentialAllotTotal},
            #{item.platformOrderPreferentialTotal},
            #{item.ptypeCommissionTotal},
            #{item.platformStockId}, #{item.platformStockCode},#{item.disedInitialPrice}, #{item.disedInitialTotal},
            #{item.disedPrice},#{item.disedTotal},
            #{item.processState},#{item.comboShareScale},#{item.displayCustomInfo},#{item.verifyCode},#{item.flowChannel},
            #{item.platformQcResult},#{item.platformQcResultDesc},#{item.platformIdentifyResult},#{item.platformIdentifyResultDesc})
        </foreach>
    </insert>

    <insert id="insertSaleOrderDetailExtend">
        INSERT INTO pl_eshop_sale_order_detail_extend (`id`, `otype_id`, `profile_id`, `eshop_order_id`,
                                                       `eshop_order_detail_id`,
                                                       `mall_deduction_rate`, `mall_deduction_fee`,
                                                       `anchor_order_preferential_total`,
                                                       `anchor_ptype_preferential_total`,
                                                       `platform_order_subsidy_total`, `platform_ptype_subsidy_total`,
                                                       `gift`,`national_subsidy_total`)
        VALUES (#{id}, #{otypeId}, #{profileId}, #{eshopOrderId}, #{detailId}, #{mallDeductionRate},
                #{mallDeductionFee}, #{anchorOrderPreferentialTotal}, #{anchorPtypePreferentialTotal},
                #{platformOrderSubsidyTotal}, #{platformPtypeSubsidyTotal}, #{gift},#{nationalSubsidyTotal})
    </insert>

    <insert id="batchInsertSaleOrderDetailExtend">
        INSERT INTO pl_eshop_sale_order_detail_extend
        (`id`,`otype_id`,`profile_id`,`eshop_order_id`,`eshop_order_detail_id`,
        `mall_deduction_rate`,`mall_deduction_fee`,`anchor_order_preferential_total`,`anchor_ptype_preferential_total`,
        `platform_order_subsidy_total`,`platform_ptype_subsidy_total`,`gift`,`national_subsidy_total`)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.id},#{item.otypeId},#{item.profileId},#{item.eshopOrderId},#{item.detailId},
            #{item.mallDeductionRate},#{item.mallDeductionFee},#{item.anchorOrderPreferentialTotal},#{item.anchorPtypePreferentialTotal},
            #{item.platformOrderSubsidyTotal},#{item.platformPtypeSubsidyTotal},#{item.gift},#{item.nationalSubsidyTotal})
        </foreach>
    </insert>

    <insert id="insertSaleOrderInvoiceBatch">
        insert into pl_eshop_sale_order_invoice
        (`id`, `profile_id`, `eshop_order_id`, `invoice_required`, `invoice_type`, `invoice_total`, `invoice_state`,
        `invoice_title`, `invoice_code`, invoice_special_info, invoice_remark, invoice_category, invoice_company,
        invoice_register_addr, invoice_register_phone, invoice_bank, invoice_bank_account)
        values
        <foreach collection="list" item="order" separator=",">
            (#{order.invoiceInfo.id}, #{order.invoiceInfo.profileId}, #{order.invoiceInfo.eshopOrderId},
            #{order.invoiceInfo.invoiceRequired}, #{order.invoiceInfo.invoiceType}, #{order.invoiceInfo.invoiceTotal},
            #{order.invoiceInfo.invoiceState},
            #{order.invoiceInfo.invoiceTitle}, #{order.invoiceInfo.invoiceCode},
            #{order.invoiceInfo.invoiceSpecialInfo}, #{order.invoiceInfo.invoiceRemark},
            #{order.invoiceInfo.invoiceCategory},
            #{order.invoiceInfo.invoiceCompany}, #{order.invoiceInfo.invoiceRegisterAddr},
            #{order.invoiceInfo.invoiceRegisterPhone}, #{order.invoiceInfo.invoiceBank},
            #{order.invoiceInfo.invoiceBankAccount})
        </foreach>
    </insert>

    <insert id="insertSaleOrderInvoice">
        insert into pl_eshop_sale_order_invoice
        (`id`, `profile_id`, `eshop_order_id`, `invoice_required`, `invoice_type`, `invoice_total`, `invoice_state`,
         `invoice_title`, `invoice_code`, invoice_special_info, invoice_remark, invoice_category, invoice_company,
         invoice_register_addr, invoice_register_phone, invoice_bank, invoice_bank_account, secret_id)
            value
            (#{id}, #{profileId}, #{eshopOrderId}, #{invoiceRequired}, #{invoiceType}, #{invoiceTotal}, #{invoiceState},
             #{invoiceTitle}, #{invoiceCode}, #{invoiceSpecialInfo}, #{invoiceRemark}, #{invoiceCategory},
             #{invoiceCompany}, #{invoiceRegisterAddr}, #{invoiceRegisterPhone}, #{invoiceBank}, #{invoiceBankAccount},
             #{secretId})
    </insert>

    <insert id="batchInsertSaleOrderInvoice">
        insert into pl_eshop_sale_order_invoice
        (`id`, `profile_id`, `eshop_order_id`, `invoice_required`, `invoice_type`, `invoice_total`, `invoice_state`,
         `invoice_title`, `invoice_code`, invoice_special_info, invoice_remark, invoice_category, invoice_company,
         invoice_register_addr, invoice_register_phone, invoice_bank, invoice_bank_account, secret_id)
            values
        <foreach collection="list" item="invoiceInfo" separator=",">
            (#{invoiceInfo.id}, #{invoiceInfo.profileId}, #{invoiceInfo.eshopOrderId}, #{invoiceInfo.invoiceRequired}, #{invoiceInfo.invoiceType}, #{invoiceInfo.invoiceTotal}, #{invoiceInfo.invoiceState},
             #{invoiceInfo.invoiceTitle}, #{invoiceInfo.invoiceCode}, #{invoiceInfo.invoiceSpecialInfo}, #{invoiceInfo.invoiceRemark}, #{invoiceInfo.invoiceCategory},
             #{invoiceInfo.invoiceCompany}, #{invoiceInfo.invoiceRegisterAddr}, #{invoiceInfo.invoiceRegisterPhone}, #{invoiceInfo.invoiceBank}, #{invoiceInfo.invoiceBankAccount},
             #{invoiceInfo.secretId})
        </foreach>
    </insert>

    <insert id="insertDetailDistribution">
        insert into pl_eshop_sale_order_detail_distribution_buyer
        (profile_id, eshop_order_id, eshop_order_detail_id, eshop_id, buyer_price, buyer_total, buyer_discount,
         buyer_dised_initial_price, buyer_dised_initial_total,
         buyer_ptype_preferential_total, buyer_order_preferential_allot_total, buyer_dised_taxed_price,
         buyer_dised_taxed_total, buyer_tax_rate,
         buyer_tax_total, buyer_dised_price, buyer_dised_total, distribution_buyer_trade_detail_id)
        values (#{profileId}, #{eshopOrderId}, #{eshopOrderDetailId}, #{eshopId}, #{buyerPrice}, #{buyerTotal},
                #{buyerDiscount},
                #{buyerDisedInitialPrice}, #{buyerDisedInitialTotal}, #{buyerPtypePreferentialTotal},
                #{buyerOrderPreferentialAllotTotal},
                #{buyerDisedTaxedPrice}, #{buyerDisedTaxedTotal}, #{buyerTaxRate}, #{buyerTaxTotal}, #{buyerDisedPrice},
                #{buyerDisedTotal}, #{distributionBuyerTradeDetailId})
        ON DUPLICATE KEY
            UPDATE `eshop_order_detail_id`=#{eshopOrderDetailId},
                   `profile_id`=#{profileId}
    </insert>

    <insert id="batchInsertDetailDistribution">
        insert into pl_eshop_sale_order_detail_distribution_buyer
        (profile_id, eshop_order_id, eshop_order_detail_id, eshop_id, buyer_price, buyer_total, buyer_discount,
         buyer_dised_initial_price, buyer_dised_initial_total,
         buyer_ptype_preferential_total, buyer_order_preferential_allot_total, buyer_dised_taxed_price,
         buyer_dised_taxed_total, buyer_tax_rate,
         buyer_tax_total, buyer_dised_price, buyer_dised_total, distribution_buyer_trade_detail_id)
        values
        <foreach collection="list" item="detailDistribution" separator=",">
            (#{detailDistribution.profileId}, #{detailDistribution.eshopOrderId}, #{detailDistribution.eshopOrderDetailId}, #{detailDistribution.eshopId}, #{detailDistribution.buyerPrice}, #{detailDistribution.buyerTotal},
                #{detailDistribution.buyerDiscount},
                #{detailDistribution.buyerDisedInitialPrice}, #{detailDistribution.buyerDisedInitialTotal}, #{detailDistribution.buyerPtypePreferentialTotal},
                #{detailDistribution.buyerOrderPreferentialAllotTotal},
                #{detailDistribution.buyerDisedTaxedPrice}, #{detailDistribution.buyerDisedTaxedTotal}, #{detailDistribution.buyerTaxRate}, #{detailDistribution.buyerTaxTotal}, #{detailDistribution.buyerDisedPrice},
                #{detailDistribution.buyerDisedTotal}, #{detailDistribution.distributionBuyerTradeDetailId})
        </foreach>
        ON DUPLICATE KEY
            UPDATE `eshop_order_detail_id`=VALUES(eshop_order_detail_id),
                   `profile_id`=VALUES(profile_id)
    </insert>

    <insert id="insertDetailComboDistribution">
        insert into pl_eshop_sale_order_detail_combo_distribution_buyer
        (`profile_id`, `eshop_order_combo_row_id`, `eshop_order_id`, `combo_id`, `trade_order_detail_id`, buyer_price,
         buyer_total, buyer_discount, buyer_dised_initial_price, buyer_dised_initial_total,
         buyer_ptype_preferential_total, buyer_order_preferential_allot_total, buyer_dised_taxed_price,
         buyer_dised_taxed_total, buyer_tax_rate,
         buyer_tax_total, buyer_dised_price, buyer_dised_total, distribution_buyer_trade_detail_id)
        values (#{profileId}, #{eshopOrderComboRowId}, #{eshopOrderId}, #{comboId}, #{tradeOrderDetailId},
                #{buyerPrice}, #{buyerTotal}, #{buyerDiscount},
                #{buyerDisedInitialPrice}, #{buyerDisedInitialTotal}, #{buyerPtypePreferentialTotal},
                #{buyerOrderPreferentialAllotTotal},
                #{buyerDisedTaxedPrice}, #{buyerDisedTaxedTotal}, #{buyerTaxRate}, #{buyerTaxTotal}, #{buyerDisedPrice},
                #{buyerDisedTotal}, #{distributionBuyerTradeDetailId})
        ON DUPLICATE KEY
            UPDATE `eshop_order_combo_row_id`=#{eshopOrderComboRowId},
                   `profile_id`=#{profileId}
    </insert>

    <insert id="batchInsertDetailComboDistribution">
        insert into pl_eshop_sale_order_detail_combo_distribution_buyer
        (`profile_id`,`eshop_order_id`,`eshop_order_combo_row_id`,`combo_id`,
         `trade_order_detail_id`,buyer_price,buyer_total,
         buyer_discount,buyer_dised_initial_price,buyer_dised_initial_total,
        buyer_ptype_preferential_total,buyer_order_preferential_allot_total,
         buyer_dised_taxed_price,buyer_dised_taxed_total,buyer_tax_rate,buyer_tax_total,
         buyer_dised_price,buyer_dised_total)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.profileId},#{item.eshopOrderId}, #{item.eshopOrderComboRowId}, #{item.comboId},
            #{item.tradeOrderDetailId}, #{item.buyerPrice},
            #{item.buyerTotal}, #{item.buyerDiscount}, #{item.buyerDisedInitialPrice}, #{item.buyerDisedInitialTotal},
            #{item.buyerPtypePreferentialTotal}, #{item.buyerOrderPreferentialAllotTotal},
            #{item.buyerDisedTaxedPrice}, #{item.buyerDisedTaxedTotal}, #{item.buyerTaxRate}, #{item.buyerTaxTotal},
            #{item.buyerDisedPrice},#{item.buyerDisedTotal})
        </foreach>
    </insert>

    <insert id="insertOrderComboRow">
        insert into pl_eshop_sale_order_detail_combo
        (eshop_order_detail_combo_row_id, profile_id, eshop_order_id, combo_id, dised_taxed_price, qty,
         dised_taxed_total, create_time,
         trade_order_detail_id, dised_initial_price, dised_initial_total, dised_price, dised_total,
         tax_total, price, total, trade_total, trade_price, ptype_service_fee,
         buyer_message, seller_memo, deliver_required, ptype_preferential_total,
         platform_ptype_preferential_total, order_preferential_allot_total, discount,
         ptype_commission_total, platform_pic_url, platform_order_preferential_total, remark, mall_deduction_rate,
         mall_deduction_fee,
         anchor_order_preferential_total, anchor_ptype_preferential_total, platform_order_subsidy_total,
         platform_ptype_subsidy_total, gift)
            value
            (#{eshopOrderDetailComboRowId}, #{profileId}, #{eshopOrderId}, #{comboId}, #{disedTaxedPrice}, #{qty},
             #{disedTaxedTotal},
             #{createTime}, #{tradeOrderDetailId}, #{disedInitialPrice}, #{disedInitialTotal}, #{disedPrice},
             #{disedTotal}, #{taxTotal}, #{price}, #{total}, #{tradeTotal},
             #{tradePrice}, #{ptypeServiceFee}, #{buyerMessage}, #{sellerMemo}, #{deliverRequired},
             #{ptypePreferentialTotal}, #{platformPtypePreferentialTotal},
             #{orderPreferentialAllotTotal}, #{discount}, #{ptypeCommissionTotal},
             #{platformPicUrl}, #{platformOrderPreferentialTotal}, #{remark}, #{mallDeductionRate}, #{mallDeductionFee},
             #{anchorOrderPreferentialTotal}, #{anchorPtypePreferentialTotal}, #{platformOrderSubsidyTotal},
             #{platformPtypeSubsidyTotal}, #{gift})
    </insert>
    <insert id="batchInsertOrderComboRow">
        insert into pl_eshop_sale_order_detail_combo
        (eshop_order_detail_combo_row_id, profile_id, eshop_order_id, combo_id, dised_taxed_price, qty,
        dised_taxed_total, create_time,
        trade_order_detail_id,
        tax_total, price, total, trade_total, trade_price, ptype_service_fee,
        buyer_message, seller_memo, deliver_required, ptype_preferential_total,
        platform_ptype_preferential_total, order_preferential_allot_total, discount,
        ptype_commission_total,platform_pic_url, dised_price,
        dised_total,platform_order_preferential_total,remark,mall_deduction_rate,mall_deduction_fee,
        anchor_order_preferential_total,anchor_ptype_preferential_total,platform_order_subsidy_total,platform_ptype_subsidy_total,gift,
         `national_subsidy_total`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.eshopOrderDetailComboRowId}, #{item.profileId}, #{item.eshopOrderId}, #{item.comboId},
            #{item.disedTaxedPrice},
            #{item.qty}, #{item.disedTaxedTotal},
            #{item.createTime}, #{item.tradeOrderDetailId}, #{item.taxTotal}, #{item.price}, #{item.total},
            #{item.tradeTotal},
            #{item.tradePrice}, #{item.ptypeServiceFee}, #{item.buyerMessage}, #{item.sellerMemo},
            #{item.deliverRequired},
            #{item.ptypePreferentialTotal}, #{item.platformPtypePreferentialTotal},
            #{item.orderPreferentialAllotTotal}, #{item.discount},
            #{item.ptypeCommissionTotal},
            #{item.platformPicUrl},#{item.disedPrice},
            #{item.disedTotal},#{item.platformOrderPreferentialTotal},#{item.remark},#{item.mallDeductionRate},#{item.mallDeductionFee},
            #{item.anchorOrderPreferentialTotal},#{item.anchorPtypePreferentialTotal},#{item.platformOrderSubsidyTotal},#{item.platformPtypeSubsidyTotal},#{item.gift},
             #{item.nationalSubsidyTotal})
        </foreach>
    </insert>


    <insert id="insertOrderTiming">
        insert into pl_eshop_sale_order_timing
        (eshop_order_id, profile_id, cn_service, plan_send_time, send_time, system_timing, timing_type,
         promised_collect_time,
         promised_sign_time, plan_sign_time, promised_sync_freight_time, sign_time, promised_sign_startTime)
        values (#{eshopOrderId}, #{profileId}, #{cnService}, #{planSendTime}, #{sendTime}, #{systemTiming},
                #{timingType},
                #{promisedCollectTime}, #{promisedSignTime}, #{planSignTime}, #{promisedSyncFreightTime}, #{signTime},
                #{promisedSignStartTime})
    </insert>

    <insert id="batchInsertOrderTiming">
        insert into pl_eshop_sale_order_timing
        (eshop_order_id, profile_id, cn_service, plan_send_time, send_time, system_timing, timing_type,
         promised_collect_time,
         promised_sign_time, plan_sign_time, promised_sync_freight_time, sign_time, promised_sign_startTime)
        values
        <foreach collection="list" item="timing" separator=",">
            (#{timing.eshopOrderId}, #{timing.profileId}, #{timing.cnService}, #{timing.planSendTime}, #{timing.sendTime}, #{timing.systemTiming},
                #{timing.timingType},
                #{timing.promisedCollectTime}, #{timing.promisedSignTime}, #{timing.planSignTime}, #{timing.promisedSyncFreightTime}, #{timing.signTime},
                #{timing.promisedSignStartTime})
        </foreach>
    </insert>

    <insert id="insertOrderTimingBatch">
        insert into pl_eshop_sale_order_timing
        (eshop_order_id, profile_id, cn_service, plan_send_time, send_time, system_timing,
        timing_type,promised_collect_time,promised_sign_time)
        values
        <foreach collection="list" item="order" separator=",">
            (#{order.timing.eshopOrderId}, #{order.timing.profileId}, #{order.timing.cnService},
            #{order.timing.planSendTime}, #{order.timing.sendTime}, #{order.timing.systemTiming},
            #{order.timing.timingType},#{promisedCollectTime},#{promisedSignTime})
        </foreach>
    </insert>

    <delete id="deleteSaleOrderDetailSendToCold">
        delete
        from pl_eshop_sale_order_detail where profile_id = #{profileId}
        AND eshop_order_id IN
        <foreach collection="eshopOrderIds" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSaleOrderSendToCold">
        delete
        from pl_eshop_sale_order where profile_id = #{profileId}
        AND id IN
        <foreach collection="eshopOrderIdList" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <update id="updateSaleOrderInvoice">
        update pl_eshop_sale_order_invoice
        set invoice_type=#{invoiceType},
            invoice_required=#{invoiceRequired},
            invoice_total=#{invoiceTotal},
            invoice_state=#{invoiceState},
            invoice_title=#{invoiceTitle},
            invoice_code=#{invoiceCode},
            invoice_special_info=#{invoiceSpecialInfo},
            invoice_remark=#{invoiceRemark},
            invoice_category=#{invoiceCategory},
            invoice_company=#{invoiceCompany},
            invoice_register_addr=#{invoiceRegisterAddr},
            invoice_register_phone=#{invoiceRegisterPhone},
            invoice_bank=#{invoiceBank},
            invoice_bank_account=#{invoiceBankAccount},
            secret_id=#{secretId}
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </update>

    <update id="batchUpdateSaleOrderInvoice">
        <foreach collection="list" item="invoice" separator=";">
        update pl_eshop_sale_order_invoice
        set invoice_type=#{invoice.invoiceType},
            invoice_required=#{invoice.invoiceRequired},
            invoice_total=#{invoice.invoiceTotal},
            invoice_state=#{invoice.invoiceState},
            invoice_title=#{invoice.invoiceTitle},
            invoice_code=#{invoice.invoiceCode},
            invoice_special_info=#{invoice.invoiceSpecialInfo},
            invoice_remark=#{invoice.invoiceRemark},
            invoice_category=#{invoice.invoiceCategory},
            invoice_company=#{invoice.invoiceCompany},
            invoice_register_addr=#{invoice.invoiceRegisterAddr},
            invoice_register_phone=#{invoice.invoiceRegisterPhone},
            invoice_bank=#{invoice.invoiceBank},
            invoice_bank_account=#{invoice.invoiceBankAccount},
            secret_id=#{invoice.secretId}
        where profile_id = #{invoice.profileId}
          and eshop_order_id = #{invoice.eshopOrderId}
        </foreach>
    </update>

    <update id="updateSaleOrderDistributionBuyer">
        update pl_eshop_sale_order_distribution_buyer
        set distribution_buyer_trade_id=#{distributionBuyerTradeId}
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </update>

    <update id="batchUpdateSaleOrderDistributionBuyer">
        <foreach collection="list" item="distribution" separator=";">
        update pl_eshop_sale_order_distribution_buyer
        set distribution_buyer_trade_id=#{distribution.distributionBuyerTradeId}
        where profile_id = #{distribution.profileId}
          and eshop_order_id = #{distribution.eshopOrderId}
        </foreach>
    </update>

    <select id="queryOrderInvoice" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderInvoiceInfo">
        select *
        from pl_eshop_sale_order_invoice
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </select>

    <select id="queryDetailDistribution"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetailDistribution">
        select *
        from pl_eshop_sale_order_detail_distribution
        where profile_id = #{profileId}
          and eshop_order_detail_id = #{eshopOrderDetailId}
    </select>

    <select id="queryAllDetailDistribution"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetailDistribution">
        select *
        from pl_eshop_sale_order_detail_distribution
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </select>

    <select id="querySimpleDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail">
        select
        d.id,d.eshop_order_id,d.ktype_id,d.profile_id,d.qty,d.unit,d.mapping_state,d.trade_order_detail_id,d.unit_qty,d.otype_id,
        d.qty,d.dised_taxed_total,d.total,d.dised_taxed_price,d.price,d.tax_total,platform_ptype_pic_url,d.deliver_required,
        d.platform_ptype_id,d.platform_sku_id,d.platform_ptype_xcode,d.platform_properties_name,d.platform_ptype_name,
        d.ptype_id,d.sku_id,d.deliver_required,d.stock_sync_rule_id,d.local_refund_state,d.local_refund_process_state,
        d.platform_detail_trade_state,d.deleted,d.order_sale_type,d.process_state,d.combo_row_id,d.re_send_state
        from pl_eshop_sale_order_detail d
        <if test="eshopId!=null or (tradeOrderIdList!=null and tradeOrderIdList.size()>0)">
            left join pl_eshop_sale_order o ON d.profile_id=o.profile_id AND d.eshop_order_id=o.id
        </if>
        where d.profile_id=#{profileId}
        <if test="eshopOrderId!=null">
            and d.eshop_order_id=#{eshopOrderId}
        </if>
        <if test="eshopOrderDetailId!=null">
            and d.id=#{eshopOrderDetailId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            and d.eshop_order_id in
            <foreach collection="eshopOrderIds" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            and d.id in
            <foreach collection="eshopOrderDetailIds" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="tradeOrderDetailIds!=null and tradeOrderDetailIds.size()>0">
            and d.trade_order_detail_id in
            <foreach collection="tradeOrderDetailIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="eshopId!=null">
            and o.otype_id=#{eshopId}
        </if>
        <if test="tradeOrderIdList!=null and tradeOrderIdList.size()>0">
            and o.trade_order_id in
            <foreach collection="tradeOrderIdList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="mappingState !=null and  mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            and d.mapping_state=0
        </if>
        <if test="mappingState !=null and  mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            and d.mapping_state=1
        </if>
    </select>
    <select id="queryInvoice" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderInvoiceInfo">
        select *
        from pl_eshop_sale_order_invoice invoice
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
        limit 1
    </select>

    <select id="queryExtend" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderExtendEntity">
        select *
        from pl_eshop_sale_order_extend extend
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
        limit 1
    </select>

    <update id="updateSaleOrder">
        update pl_eshop_sale_order
        set ktype_id=#{ktypeId},
        otype_id=#{otypeId},
        btype_id=#{btypeId},
        trade_order_id=#{tradeOrderId},
        buyer_id=#{buyerId},
        etype_id=#{etypeId},
        dtype_id=#{dtypeId},
        platform_trade_state=#{platformTradeState},
        local_trade_state=#{localTradeState},
        local_refund_process_state=#{localRefundProcessState},
        seller_flag=#{sellerFlag},
        platform_store_id=#{platformStoreId},
        platform_store_code=#{platformStoreCode},
        business_type=#{businessType},
        order_source_type=#{orderSourceType},
        deliver_type=#{deliverType},
        pay_time_type=#{payTimeType},
        trade_modified_time=#{tradeModifiedTime},
        trade_finish_time=#{tradeFinishTime},
        trade_pay_time=#{tradePayTime},
        modified_time=#{modifiedTime},
        local_freight_name=#{localFreightName},
        local_freight_code=#{localFreightCode},
        local_freight_bill_no=#{localFreightBillNo},
        platform_freight_name=#{platformFreightName},
        platform_freight_code=#{platformFreightCode},
        customer_expected_freight_name=#{customerExpectedFreightName},
        customer_expected_freight_code=#{customerExpectedFreightCode},
        trade_total=#{tradeTotal},
        buyer_trade_total=#{buyerTradeTotal},
        buyer_unpaid_total=#{buyerUnpaidTotal},
        dised_taxed_total=#{disedTaxedTotal},
        ptype_preferential_total=#{ptypePreferentialTotal},
        tax_total=#{taxTotal},
        total=#{total},
        order_buyer_freight_fee=#{orderBuyerFreightFee},
        ptype_service_fee=#{ptypeServiceFee},
        mapping_state=#{mappingState},
        order_deliver_required=#{orderDeliverRequired},
        seller_memo=#{sellerMemo},
        seller_flag=#{sellerFlag},
        buyer_message=#{buyerMessage},
        remark=#{remark},
        pay_no=#{payNo},
        deleted=#{deleted},
        local_refund_state=#{localRefundState},
        platform_stock_id=#{platformStockId},
        <if test="needUpdateProcessState!=null and needUpdateProcessState==true">
            process_state=#{processState},
        </if>
        platform_stock_code=#{platformStockCode},
        <if test="tradeCreateTime!=null">
            trade_create_time=#{tradeCreateTime},
        </if>
        order_sale_type=#{orderSaleType},
        self_delivery_mode=#{selfDeliveryMode},
        salesman=#{salesman},
        summary=#{summary},
        platform_ptype_preferential_total=#{platformPtypePreferentialTotal},
        order_preferential_allot_total=#{orderPreferentialAllotTotal},
        ptype_commission_total=#{ptypeCommissionTotal},
        distribution_dised_taxed_total=#{distributionDisedTaxedTotal},
        buyer_paid_total=#{buyerPaidTotal},
        platform_order_preferential_total=#{platformOrderPreferentialTotal},
        pay_btype_id=#{payBtypeId},
        commission_btype_id=#{commissionBtypeId},
        mode_of_payment=#{modeOfPayment},
        merchant_payment_account=#{merchantPaymentAccount},
        payway_id=#{paywayId},
        re_send_state=#{reSendState},
        deliver_process_type=#{deliverProcessType},
        platform_parent_order_id=#{platformParentOrderId},
        platform_distributor_name=#{platformDistributorName},
        platform_distributor_id=#{platformDistributorId}
        where profile_id=#{profileId} and id=#{id}
    </update>

    <update id="batchUpdateSaleOrder">
        <foreach collection="list" item="order" separator=";">
        update pl_eshop_sale_order
        set ktype_id=#{order.ktypeId},
        otype_id=#{order.otypeId},
        btype_id=#{order.btypeId},
        trade_order_id=#{order.tradeOrderId},
        buyer_id=#{order.buyerId},
        etype_id=#{order.etypeId},
        dtype_id=#{order.dtypeId},
        platform_trade_state=#{order.platformTradeState},
        local_trade_state=#{order.localTradeState},
        local_refund_process_state=#{order.localRefundProcessState},
        seller_flag=#{order.sellerFlag},
        platform_store_id=#{order.platformStoreId},
        platform_store_code=#{order.platformStoreCode},
        business_type=#{order.businessType},
        order_source_type=#{order.orderSourceType},
        deliver_type=#{order.deliverType},
        pay_time_type=#{order.payTimeType},
        trade_modified_time=#{order.tradeModifiedTime},
        trade_finish_time=#{order.tradeFinishTime},
        trade_pay_time=#{order.tradePayTime},
        modified_time=#{order.modifiedTime},
        local_freight_name=#{order.localFreightName},
        local_freight_code=#{order.localFreightCode},
        local_freight_bill_no=#{order.localFreightBillNo},
        platform_freight_name=#{order.platformFreightName},
        platform_freight_code=#{order.platformFreightCode},
        customer_expected_freight_name=#{order.customerExpectedFreightName},
        customer_expected_freight_code=#{order.customerExpectedFreightCode},
        trade_total=#{order.tradeTotal},
        buyer_trade_total=#{order.buyerTradeTotal},
        buyer_unpaid_total=#{order.buyerUnpaidTotal},
        dised_taxed_total=#{order.disedTaxedTotal},
        ptype_preferential_total=#{order.ptypePreferentialTotal},
        tax_total=#{order.taxTotal},
        total=#{order.total},
        order_buyer_freight_fee=#{order.orderBuyerFreightFee},
        ptype_service_fee=#{order.ptypeServiceFee},
        mapping_state=#{order.mappingState},
        order_deliver_required=#{order.orderDeliverRequired},
        seller_memo=#{order.sellerMemo},
        seller_flag=#{order.sellerFlag},
        buyer_message=#{order.buyerMessage},
        remark=#{order.remark},
        pay_no=#{order.payNo},
        deleted=#{order.deleted},
        local_refund_state=#{order.localRefundState},
        platform_stock_id=#{order.platformStockId},
        <if test="order.needUpdateProcessState!=null and order.needUpdateProcessState==true">
            process_state=#{order.processState},
        </if>
        platform_stock_code=#{order.platformStockCode},
        <if test="order.tradeCreateTime!=null">
            trade_create_time=#{order.tradeCreateTime},
        </if>
        order_sale_type=#{order.orderSaleType},
        self_delivery_mode=#{order.selfDeliveryMode},
        salesman=#{order.salesman},
        summary=#{order.summary},
        platform_ptype_preferential_total=#{order.platformPtypePreferentialTotal},
        order_preferential_allot_total=#{order.orderPreferentialAllotTotal},
        ptype_commission_total=#{order.ptypeCommissionTotal},
        distribution_dised_taxed_total=#{order.distributionDisedTaxedTotal},
        buyer_paid_total=#{order.buyerPaidTotal},
        platform_order_preferential_total=#{order.platformOrderPreferentialTotal},
        pay_btype_id=#{order.payBtypeId},
        commission_btype_id=#{order.commissionBtypeId},
        mode_of_payment=#{order.modeOfPayment},
        merchant_payment_account=#{order.merchantPaymentAccount},
        payway_id=#{order.paywayId},
        re_send_state=#{order.reSendState},
        deliver_process_type=#{order.deliverProcessType},
        platform_parent_order_id=#{order.platformParentOrderId},
        platform_distributor_name=#{order.platformDistributorName},
        platform_distributor_id=#{order.platformDistributorId}
        where profile_id=#{order.profileId} and id=#{order.id}
        </foreach>
    </update>

    <update id="updateSaleOrderExtend">
        update pl_eshop_sale_order_extend
        set `export_count`=#{exportCount},
        `order_number`=#{orderNumber},
        `otype_id`=#{otypeId},
        <if test="createTime!=null">
            create_time=#{createTime},
        </if>
        <if test="updateTime!=null">
            update_time=#{updateTime},
        </if>
        `deliver_send_state`=#{deliverSendState},
        `installation_service_provider`=#{installationServiceProvider},
        `collect_time`=#{collectTime},
        `collect_customer`=#{collectCustomer},
        `gather_status`=#{gatherStatus},
        `payment_mode`=#{paymentMode},
        `hold_time`=#{holdTime},
        `platform_required`=#{platformRequired},
        `platform_json`=#{platformJson},
        `group_header_name`=#{groupHeaderName},
        `trade_summary_md5`=#{tradeSummaryMd5},
        `buyer_unique_mark`=#{buyerUniqueMark},
        `supplier_id`=#{supplierId},
        `advance_total`=#{advanceTotal},
        `pickup_code`=#{pickupCode},
        `platform_dispatcher_name`=#{platformDispatcherName},
        `platform_dispather_mobile`=#{platformDispatherMobile},
        `logistics_status`=#{logisticsStatus},
        `confirm_status`=#{confirmStatus},
        `platform_send_time`=#{platformSendTime},
        `real_buyer_id`=#{realBuyerId},
        `pick_up_address_id`=#{pickUpAddressId},
        `seller_flag_memo`=#{sellerFlagMemo},
        `platform_qc_result`=#{platformQcResult},
        `platform_identify_result`=#{platformIdentifyResult},
        `flow_channel`=#{flowChannel},
        `mall_deduction_fee`=#{mallDeductionFee},
        `group_title`=#{groupTitle},
        `group_no`=#{groupNo},
        take_goods_code=#{takeGoodsCode},
        platform_hash_order_main=#{platformHashOrderMain},
        platform_hash_order_detail=#{platformHashOrderDetail},
        platform_hash_mark_list=#{platformHashMarkList},
        take_goods_code=#{takeGoodsCode},
        `platform_quality_org_id`=#{platformQualityOrgId},
        `platform_quality_org_name`=#{platformQualityOrgName},
        `platform_quality_warehouse_code`=#{platformQualityWarehouseCode},
        `platform_quality_warehouse_name`=#{platformQualityWarehouseName},
        `is_draft`=#{isDraft},
        `anchor_order_preferential_total`=#{anchorOrderPreferentialTotal},
        `platform_order_subsidy_total`=#{platformOrderSubsidyTotal},
        `sale_period_num`=#{salePeriodNum},
        `current_period_num`=#{currentPeriodNum}
        where profile_id=#{profileId} and eshop_order_id=#{eshopOrderId}
    </update>

    <update id="batchUpdateSaleOrderExtend">
        <foreach collection="list" item="extend" separator=";">
        update pl_eshop_sale_order_extend
        set `export_count`=#{extend.exportCount},
        `order_number`=#{extend.orderNumber},
        `otype_id`=#{extend.otypeId},
        <if test="extend.createTime!=null">
            create_time=#{extend.createTime},
        </if>
        <if test="extend.updateTime!=null">
            update_time=#{extend.updateTime},
        </if>
        `deliver_send_state`=#{extend.deliverSendState},
        `installation_service_provider`=#{extend.installationServiceProvider},
        `collect_time`=#{extend.collectTime},
        `collect_customer`=#{extend.collectCustomer},
        `gather_status`=#{extend.gatherStatus},
        `payment_mode`=#{extend.paymentMode},
        `hold_time`=#{extend.holdTime},
        `platform_required`=#{extend.platformRequired},
        `platform_json`=#{extend.platformJson},
        `group_header_name`=#{extend.groupHeaderName},
        `trade_summary_md5`=#{extend.tradeSummaryMd5},
        `buyer_unique_mark`=#{extend.buyerUniqueMark},
        `supplier_id`=#{extend.supplierId},
        `advance_total`=#{extend.advanceTotal},
        `pickup_code`=#{extend.pickupCode},
        `platform_dispatcher_name`=#{extend.platformDispatcherName},
        `platform_dispather_mobile`=#{extend.platformDispatherMobile},
        `logistics_status`=#{extend.logisticsStatus},
        `confirm_status`=#{extend.confirmStatus},
        `platform_send_time`=#{extend.platformSendTime},
        `real_buyer_id`=#{extend.realBuyerId},
        `pick_up_address_id`=#{extend.pickUpAddressId},
        `seller_flag_memo`=#{extend.sellerFlagMemo},
        `platform_qc_result`=#{extend.platformQcResult},
        `platform_identify_result`=#{extend.platformIdentifyResult},
        `flow_channel`=#{extend.flowChannel},
        `mall_deduction_fee`=#{extend.mallDeductionFee},
        `group_title`=#{extend.groupTitle},
        `group_no`=#{extend.groupNo},
        take_goods_code=#{extend.takeGoodsCode},
        platform_hash_order_main=#{extend.platformHashOrderMain},
        platform_hash_order_detail=#{extend.platformHashOrderDetail},
        platform_hash_mark_list=#{extend.platformHashMarkList},
        take_goods_code=#{extend.takeGoodsCode},
        `platform_quality_org_id`=#{extend.platformQualityOrgId},
        `platform_quality_org_name`=#{extend.platformQualityOrgName},
        `platform_quality_warehouse_code`=#{extend.platformQualityWarehouseCode},
        `platform_quality_warehouse_name`=#{extend.platformQualityWarehouseName},
        `is_draft`=#{extend.isDraft},
        `anchor_order_preferential_total`=#{extend.anchorOrderPreferentialTotal},
        `platform_order_subsidy_total`=#{extend.platformOrderSubsidyTotal},
        `sale_period_num`=#{extend.salePeriodNum},
        `current_period_num`=#{extend.currentPeriodNum},
        `national_subsidy_total`=#{extend.nationalSubsidyTotal}
        where profile_id=#{extend.profileId} and eshop_order_id=#{extend.eshopOrderId}
        </foreach>
    </update>

    <update id="updateSaleOrderTiming">
        update pl_eshop_sale_order_timing
        set `cn_service`=#{cnService},
        `plan_send_time`=#{planSendTime},
        `send_time`=#{sendTime},
        `plan_sign_time`=#{planSignTime},
        `promised_collect_time`=#{promisedCollectTime},
        `promised_sign_time`=#{promisedSignTime},
        `promised_sync_freight_time`=#{promisedSyncFreightTime},
        <if test="createTime!=null">
            create_time=#{createTime},
        </if>
        <if test="updateTime!=null">
            update_time=#{updateTime},
        </if>
        `system_timing`=#{systemTiming},
        `timing_type`=#{timingType},
        `promised_sign_startTime`=#{promisedSignStartTime},
        `sign_time`=#{signTime}
        where profile_id=#{profileId} and eshop_order_id=#{eshopOrderId}
    </update>

    <update id="updateSaleOrderDetail">
        UPDATE pl_eshop_sale_order_detail
        set `platform_ptype_id`                 = #{platformPtypeId},
            `platform_sku_id`                   = #{platformSkuId},
            `platform_ptype_name`               = #{platformPtypeName},
            `platform_properties_name`          = #{platformPropertiesName},
            `platform_ptype_xcode`              = #{platformPtypeXcode},
            `platform_ptype_pic_url`            = #{platformPtypePicUrl},
            `platform_store_code`               = #{platformStoreCode},
            `trade_total`                       = #{tradeTotal},
            `dised_taxed_total`                 = #{disedTaxedTotal},
            `refund_total`                      = #{refundTotal},
            `tax_total`                         = #{taxTotal},
            `total`                             = #{total},
            `trade_price`                       = #{tradePrice},
            `dised_taxed_price`                 = #{disedTaxedPrice},
            `dised_price`                       = #{disedPrice},
            `dised_total`                       = #{disedTotal},
            `price`                             = #{price},
            `qty`                               = #{qty},
            `unit_qty`                          = #{unitQty},
            `sub_qty`                           = #{subQty},
            `refund_qty`                        = #{refundQty},
            `deleted`                           = #{deleted},
            `local_refund_process_state`        = #{localRefundProcessState},
            `platform_detail_trade_state`       = #{platformDetailTradeState},
            `tax_rate`                          = #{taxRate},
            `discount`                          = #{discount},
            `local_refund_state`                = #{localRefundState},
            `order_sale_type`                   = #{orderSaleType},
            `ptype_preferential_total`          = #{ptypePreferentialTotal},
            `platform_ptype_preferential_total` = #{platformPtypePreferentialTotal},
            `order_preferential_allot_total`    = #{orderPreferentialAllotTotal},
            `platform_order_preferential_total` = #{platformOrderPreferentialTotal},
            `deliver_required`                  = #{deliverRequired},
            `ptype_service_fee`                 = #{ptypeServiceFee},
            `ptype_commission_total`            =#{ptypeCommissionTotal},
            re_send_state                       =#{reSendState},
            batchno                             =#{batchno},
            produce_date                        =#{produceDate},
            expire_date                         =#{expireDate},
            platform_stock_id                   =#{platformStockId},
            platform_stock_code                 =#{platformStockCode},
            ktype_id                            =#{ktypeId}
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <update id="updateSaleOrderDetailExtend">
        UPDATE pl_eshop_sale_order_detail_extend
        SET `mall_deduction_rate`             = #{mallDeductionRate},
            `mall_deduction_fee`              = #{mallDeductionFee},
            `anchor_order_preferential_total` = #{anchorOrderPreferentialTotal},
            `anchor_ptype_preferential_total` = #{anchorPtypePreferentialTotal},
            `platform_order_subsidy_total`    = #{platformOrderSubsidyTotal},
            `platform_ptype_subsidy_total`    = #{platformPtypeSubsidyTotal},
            `national_subsidy_total`    = #{nationalSubsidyTotal},
            `gift`                            = #{gift}
        WHERE profile_id = #{profileId}
          and eshop_order_detail_id = #{detailId}
    </update>

    <update id="deleteSaleOrder">
        UPDATE pl_eshop_sale_order
        SET deleted=1
        WHERE profile_id=#{profileId}
        and process_state=0
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            AND id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteSaleOrderDetails">
        delete from pl_eshop_sale_order_detail
        where profile_id=#{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id =#{eshopOrderId}
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <delete id="deleteSaleOrderDetailExtends">
        delete from pl_eshop_sale_order_detail_extend
        where profile_id=#{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id =#{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="deleteSaleOrderDetailFreightList">
        delete from pl_eshop_order_detail_freight
        where create_type=0
          and (profile_id,trade_order_id,otype_id) IN(
        <foreach collection="list"  item="item"  separator=",">
            (#{item.profileId},#{item.tradeOrderId},#{item.otypeId})
        </foreach>
        )
    </delete>

    <delete id="deleteSaleOrderDetailFreightListByTradeOrderDetailId">
        delete from pl_eshop_order_detail_freight
        where create_type=0
            and profile_id=#{profileId} and otype_id=#{otypeId}
          and trade_order_id=#{tradeOrderId}  and trade_order_detail_id=#{tradeOrderDetailId}
    </delete>

    <delete id="deleteOrderComboRow">
        delete
        from pl_eshop_sale_order_detail_combo
        where profile_id = #{profileId}
        and eshop_order_id = #{eshopOrderId}
        <if test="eshopOrderDetailComboRowIds!=null and eshopOrderDetailComboRowIds.size>0">
            AND eshop_order_detail_combo_row_id IN
            <foreach collection="eshopOrderDetailComboRowIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="batchDeleteOrderComboRow">
        delete
        from pl_eshop_sale_order_detail_combo
        where profile_id = #{profileId}
        and eshop_order_id IN
        <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="eshopOrderDetailComboRowIds!=null and eshopOrderDetailComboRowIds.size>0">
            AND eshop_order_detail_combo_row_id IN
            <foreach collection="eshopOrderDetailComboRowIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="deleteOrderComboRowOnlyId">
        delete
        from pl_eshop_sale_order_detail_combo
        where profile_id = #{profileId}
            AND eshop_order_detail_combo_row_id IN
            <foreach collection="eshopOrderDetailComboRowIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
    </delete>

    <update id="deleteDetailDistribution">
        delete
        from pl_eshop_sale_order_detail_distribution_buyer
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
    <update id="deleteDetailComboDistribution">
        delete
        from pl_eshop_sale_order_detail_combo_distribution_buyer
        where profile_id = #{profileId}
        and eshop_order_id = #{eshopOrderId}
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_combo_row_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="batchDeleteDetailComboDistribution">
        delete
        from pl_eshop_sale_order_detail_combo_distribution_buyer
        where profile_id = #{profileId}
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_combo_row_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateEshopOrderRemark">
        update pl_eshop_sale_order
        set remark=#{message}
        WHERE profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="updateEshopOrderSellerMemo">
        update pl_eshop_sale_order
        set seller_memo=#{message},
            seller_flag=#{flageId}
        WHERE profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="updateEshopOrderSellerFlagMemo">
        update pl_eshop_sale_order_extend
        set seller_flag_memo=#{sellerFlagMemo}
        WHERE profile_id = #{profileId}
          and eshop_order_id = #{id}
    </update>
    <update id="updateOrderFlag">
        update pl_eshop_sale_order
        set seller_flag=#{flagId}
        WHERE profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="modifyOrderTiming">
        update pl_eshop_sale_order_timing
        set send_time=#{sendTime},
            cn_service=#{cnService},
            plan_send_time=#{planSendTime},
            system_timing=#{systemTiming},
            timing_type=#{timingType},
            `promised_collect_time`=#{promisedCollectTime},
            `plan_sign_time`=#{planSignTime},
            `promised_sign_time`=#{promisedSignTime},
            `promised_sync_freight_time`=#{promisedSyncFreightTime},
            `sign_time`=#{signTime},
            `promised_sign_startTime`=#{promisedSignStartTime}
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </update>

    <update id="batchModifyOrderTiming">
        <foreach collection="list" item="timing" separator=";">
        update pl_eshop_sale_order_timing
        set send_time=#{timing.sendTime},
            cn_service=#{timing.cnService},
            plan_send_time=#{timing.planSendTime},
            system_timing=#{timing.systemTiming},
            timing_type=#{timing.timingType},
            `promised_collect_time`=#{timing.promisedCollectTime},
            `plan_sign_time`=#{timing.planSignTime},
            `promised_sign_time`=#{timing.promisedSignTime},
            `promised_sync_freight_time`=#{timing.promisedSyncFreightTime},
            `sign_time`=#{timing.signTime},
            `promised_sign_startTime`=#{timing.promisedSignStartTime}
        where profile_id = #{timing.profileId}
          and eshop_order_id = #{timing.eshopOrderId}
        </foreach>
    </update>

    <update id="closeOrder" parameterType="java.util.List">
        <foreach collection="orderList" item="item" index="index" separator=";">
            update pl_eshop_sale_order
            set local_trade_state=5
            where profile_id=#{item.profileId} AND
            id=#{item.id}
        </foreach>
    </update>

    <update id="closeOrderDetail" parameterType="java.util.List">
        <foreach collection="orderList" item="item" index="index" separator=";">
            update pl_eshop_sale_order_detail
            set
            platform_detail_trade_state=5
            where profile_id=#{item.profileId} AND
            eshop_order_id=#{item.id}
        </foreach>
    </update>


    <update id="closeAdvanceOrder" parameterType="java.util.List">
        <foreach collection="orderList" item="item" index="index" separator=";">
            update td_orderbill_platform
            set local_trade_state=5
            where profile_id=#{item.profileId}
            AND vchcode=#{item.vchcode}
        </foreach>
    </update>

    <update id="closeAdvanceOrderDetail" parameterType="java.util.List">
        <!--        <foreach collection="orderList" item="item" index="index" separator=";">-->
        <!--            update pl_eshop_sale_order_advance_detail-->
        <!--            set-->
        <!--            mention_type=<![CDATA[if((mention_type&64)=0,mention_type|64,mention_type)]]>-->
        <!--            where profile_id=#{item.profileId} AND-->
        <!--            id=#{item.id}-->
        <!--        </foreach>-->
    </update>

    <update id="updateOrderComboRow">
        update pl_eshop_sale_order_detail_combo
        set `dised_taxed_price`                 = #{disedTaxedPrice},
            `qty`                               = #{qty},
            `dised_taxed_total`                 = #{disedTaxedTotal},
            `tax_total`                         = #{taxTotal},
            `price`                             = #{price},
            `total`                             = #{total},
            `trade_total`                       = #{tradeTotal},
            `trade_price`                       = #{tradePrice},
            `ptype_service_fee`                 = #{ptypeServiceFee},
            `seller_memo`                       = #{sellerMemo},
            `buyer_message`                     = #{buyerMessage},
            `deliver_required`                  = #{deliverRequired},
            `ptype_preferential_total`          = #{ptypePreferentialTotal},
            `platform_ptype_preferential_total` = #{platformPtypePreferentialTotal},
            `order_preferential_allot_total`    = #{orderPreferentialAllotTotal},
            `platform_order_preferential_total` = #{platformOrderPreferentialTotal},
            `discount`                          = #{discount},
            `ptype_commission_total`            = #{ptypeCommissionTotal},
            `platform_pic_url`                  = #{platformPicUrl},
            `dised_price`                       = #{disedPrice},
            `dised_total`                       = #{disedTotal},
            `mall_deduction_rate`               = #{mallDeductionRate},
            `mall_deduction_fee`                = #{mallDeductionFee},
            `national_subsidy_total`                = #{nationalSubsidyTotal},
            `gift`                              = #{gift}
        WHERE profile_id = #{profileId}
          and eshop_order_detail_combo_row_id = #{eshopOrderDetailComboRowId}
    </update>

    <select id="queryListOrdersByVchcodes"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.GetSaleOrderInfoRequest"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        SELECT
        <include refid="main_order"/>
        FROM ${tableName}
        <include refid="order_jointable"/>
        WHERE `order`.profile_id=#{profileId}
        <if test="btypeIds!=null and btypeIds.size() > 0">
            AND `order`.btype_id IN
            <foreach collection="btypeIds" close=")" open="(" separator="," item="btypeId">
                #{btypeId}
            </foreach>
        </if>
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            AND `order`.id IN
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>


    <select id="queryListOrdersByVchcodesForJXC"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.GetSaleOrderInfoRequest"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        SELECT
        <include refid="main_order_new"/>
        FROM ${tableName}
        <include refid="order_jointable_new"/>
        WHERE `order`.profile_id=#{profileId}
        <if test="btypeIds!=null and btypeIds.size() > 0">
            AND `order`.btype_id IN
            <foreach collection="btypeIds" close=")" open="(" separator="," item="btypeId">
                #{btypeId}
            </foreach>
        </if>
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            AND `order`.id IN
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        GROUP BY order.id;
    </select>

    <select id="getOrderDecryptInfoByTradeIds"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformdecrypt.OrderDecryptInfo">
        SELECT es.trade_Order_id,es.profile_id,es.otype_id,e.eshop_type AS tradeForm,e.eshop_type,e.token
        ,e.eshop_account,IFNULL(ec.rds_apply_time,'') AS rdsapplytime
        FROM pl_eshop_sale_order es
        LEFT JOIN pl_eshop e ON es.otype_id = e.otype_id AND es.profile_id = e.profile_id
        LEFT JOIN pl_eshop_config ec ON ec.profile_id = e.profile_id AND e.otype_id = ec.eshop_id
        WHERE es.profile_id=#{profileId}
        <if test="id!=null and id!=0">
            and es.id=#{id}
        </if>
        <if test="otypeId!=null and otypeId!=0">
            and es.otype_id=#{otypeId}
        </if>
        <if test="tradeOrderIds != null and tradeOrderIds.size()>0">
            and es.tradeOrderId in
            <foreach collection="tradeOrderIds" item="tradeOrderId" separator="," index="i" close=")" open="(">
                #{tradeOrderId}
            </foreach>
        </if>
    </select>


    <select id="querySimpleOrder" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        select od.*,
        e.eshop_type as shopType,
        `extend`.is_draft AS 'extend.is_draft'
        from pl_eshop_sale_order od
        LEFT JOIN pl_eshop_sale_order_extend extend ON od.id = extend.eshop_order_id AND od.profile_id =
        extend.profile_id
        LEFT JOIN pl_eshop e ON od.otype_id = e.otype_id AND od.profile_id = e.profile_id
        where od.profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and od.id=#{eshopOrderId}
        </if>
        <if test="tradeOrderId!=null and tradeOrderId!=''">
            and od.trade_order_id=#{tradeOrderId}
        </if>
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `od`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
    </select>

    <select id="querySimpleOrderList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        select od.*,
        `extend`.collect_customer AS 'extend.collect_customer',
        `extend`.gather_status AS 'extend.gather_status',
        `extend`.payment_mode AS 'extend.payment_mode',
        `extend`.platform_required AS 'extend.platform_required',
        `extend`.group_header_name AS 'extend.group_header_name',
        `extend`.buyer_unique_mark AS 'extend.buyer_unique_mark',
        `extend`.trade_summary_md5 AS 'extend.trade_summary_md5',
        `extend`.advance_total AS 'extend.advance_total',
        `extend`.pickup_code AS 'extend.pickup_code',
        `extend`.platform_dispatcher_name AS 'extend.platform_dispatcher_name',
        `extend`.platform_dispather_mobile AS 'extend.platform_dispather_mobile',
        `extend`.logistics_status AS 'extend.logistics_status',
        `extend`.confirm_status AS 'extend.confirm_status',
        `extend`.pick_up_address_id AS 'extend.pick_up_address_id',
        `extend`.real_buyer_id AS 'extend.real_buyer_id',
        `extend`.seller_flag_memo AS 'extend.seller_flag_memo',
        `extend`.eshop_order_id AS 'extend.eshop_order_id'
        from pl_eshop_sale_order od
        left join pl_eshop_sale_order_extend extend on od.profile_id=extend.profile_id and od.id=extend.eshop_order_id
        where od.profile_id = #{profileId}
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `od`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size() > 0">
            AND `od`.trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
        <if test="eshopOrderIdList !=null and eshopOrderIdList.size() > 0">
            AND `od`.id IN
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="findYfkWfkEshopOrder" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderResult">
        select
        sum(if(peso.local_trade_state >= 2 and peso.local_trade_state != 5, 1, 0)) as yfkOrderCount,
        sum(if(peso.local_trade_state = 1, 1, 0)) as WfkOrderCount,
        sum(if(peso.local_trade_state >= 2 and peso.local_trade_state != 5,
        peso.dised_taxed_total+peso.order_buyer_freight_fee+peso.ptype_service_fee, 0)) as yfkAmount,
        sum(if(peso.local_trade_state = 1,
        peso.dised_taxed_total+peso.order_buyer_freight_fee+peso.ptype_service_fee, 0)) as wfkAmount
        from pl_eshop_sale_order peso
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=peso.profile_id and blsk.object_type=2 and
            peso.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=peso.profile_id and bls.object_type=3 and
            peso.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where peso.profile_id = #{profileId} and peso.local_trade_state &gt;= 1 and peso.local_refund_state !=4 and
        peso.local_trade_state!=5 and peso.create_type =1 and peso.deleted =0
        AND (`peso`.order_sale_type not in(3,6) OR (`peso`.order_sale_type=3 AND `peso`.platform_parent_order_id=''))
        <if test="beginTime!=null">
            and peso.trade_create_time &gt;= #{beginTime}
        </if>
        <if test="endTime!=null">
            and peso.trade_create_time &lt;= #{endTime}
        </if>
        <if test="beginTime==null and endTime==null">
            and date_sub(CURDATE(), INTERVAL 29 DAY) &lt;= peso.trade_create_time
        </if>
    </select>

    <select id="findwfkEshopOrder" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderResult">
        select count(distinct (peso.id)) as wfkOrderCount,sum(peso.dised_taxed_total) +
        sum(peso.order_buyer_freight_fee)+sum(peso.ptype_service_fee) as wfkAmount
        from pl_eshop_sale_order peso
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=peso.profile_id and blsk.object_type=2 and
            peso.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=peso.profile_id and bls.object_type=3 and
            peso.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where date_sub(CURDATE(), INTERVAL 29 DAY) &lt;= peso.trade_create_time
        and peso.profile_id = #{profileId} and peso.local_trade_state =1 and peso.create_type =1 and peso.deleted =0
        AND (`peso`.order_sale_type  not in(3,6) OR (`peso`.order_sale_type=3 AND `peso`.platform_parent_order_id=''))
    </select>

    <select id="findOtherEshopOrder" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderResult">
        select count(distinct (peso.id)) as otherOrderCount,sum(peso.dised_taxed_total) +
        sum(peso.order_buyer_freight_fee)+sum(peso.ptype_service_fee) as otherAmount
        from pl_eshop_sale_order peso
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=peso.profile_id and blsk.object_type=2 and
            peso.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=peso.profile_id and bls.object_type=3 and
            peso.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where peso.profile_id = #{profileId} and peso.create_type !=1 and peso.deleted =0
        AND (`peso`.order_sale_type  not in(3,6) OR (`peso`.order_sale_type=3 AND `peso`.platform_parent_order_id=''))
        <if test="beginTime!=null">
            and peso.trade_create_time &gt;= #{beginTime}
        </if>
        <if test="endTime!=null">
            and peso.trade_create_time &lt;= #{endTime}
        </if>
        <if test="beginTime==null and endTime==null">
            and date_sub(CURDATE(), INTERVAL 29 DAY) &lt;= peso.trade_create_time
        </if>
    </select>

    <select id="getDeliverbill" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.DeliverBillEntity">
        SELECT c.bill_number,
               a.trade_order_id,
               b.trade_state,
               b.process_state,
               d.freight_billno,
               btype.fullname as freightName
        FROM td_bill_deliver a
                 LEFT JOIN `td_bill_deliver_state` b ON a.profile_id = b.profile_id AND a.vchcode = b.vchcode
                 LEFT JOIN `td_bill_core` c ON a.profile_id = c.profile_id AND a.vchcode = c.vchcode
                 LEFT JOIN `td_deliver_freight_info` d ON a.profile_id = d.profile_id AND a.vchcode = d.vchcode
                 LEFT JOIN `base_btype` btype ON btype.profile_id = d.profile_id AND btype.id = d.freight_btype_id
        WHERE a.vchcode IN (SELECT DISTINCT(vchcode)
                            FROM `td_bill_detail_deliver`
                            WHERE profile_id = #{profileId}
                              AND order_id = #{vchcode})
          AND a.profile_id = #{profileId}
          AND c.`deleted` = 0

        UNION ALL

        SELECT c.bill_number,
               a.trade_order_id,
               b.trade_state,
               b.process_state,
               d.freight_billno,
               btype.fullname as freightName
        FROM acc_bill_deliver a
                 LEFT JOIN `acc_bill_deliver_state` b ON a.profile_id = b.profile_id AND a.vchcode = b.vchcode
                 LEFT JOIN `acc_bill_core` c ON a.profile_id = c.profile_id AND a.vchcode = c.vchcode
                 LEFT JOIN `acc_deliver_freight_info` d ON a.profile_id = d.profile_id AND a.vchcode = d.vchcode
                 LEFT JOIN `base_btype` btype ON btype.profile_id = d.profile_id AND btype.id = d.freight_btype_id
        WHERE a.vchcode IN (SELECT DISTINCT(vchcode)
                            FROM `acc_bill_detail_deliver`
                            WHERE profile_id = #{profileId}
                              AND order_id = #{vchcode})
          AND a.profile_id = #{profileId}
          AND c.`deleted` = 0
    </select>

    <select id="findOrderNoMapping" resultType="integer">
        SELECT COUNT(0)
        FROM `pl_eshop_sale_order` `order`
        WHERE date_sub(CURDATE(), INTERVAL 0 DAY) &lt;= `order`.trade_create_time
          AND `order`.mapping_state = 0
          AND `order`.order_deliver_required = 1
          AND `order`.local_trade_state != 5
          AND `order`.profile_id = #{profileId}
    </select>

    <select id="queryComboDetailCount" resultType="java.lang.Integer">
        select count(1)
        from pl_eshop_sale_order_detail_combo
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </select>

    <select id="queryNormalDetailCount" resultType="java.lang.Integer">
        select count(1)
        from pl_eshop_sale_order_detail
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
          and combo_row_id = 0
    </select>

    <select id="queryAllDetailCount" resultType="java.lang.Integer">
        select count(1)
        from pl_eshop_sale_order_detail
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </select>

    <select id="queryAllSaleOrderFields"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryOrderParameter"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        SELECT `order`.id,
        `order`.trade_order_id,
        `order`.otype_id,
        `order`.profile_id,
        `order`.etype_id,
        `order`.create_type,
        `order`.creator_id,
        `order`.platform_trade_state,
        `order`.local_trade_state,
        `order`.local_refund_process_state,
        `order`.seller_flag,
        `order`.platform_store_id,
        `order`.platform_store_code,
        `order`.business_type,
        `order`.order_source_type,
        `order`.deliver_type,
        `order`.pay_time_type,
        `order`.trade_create_time,
        `order`.trade_modified_time,
        `order`.trade_finish_time,
        `order`.trade_pay_time,
        `order`.create_time,
        `order`.update_time,
        `order`.modified_time,
        `order`.local_freight_name,
        `order`.local_freight_code,
        `order`.local_freight_bill_no,
        `order`.platform_freight_name,
        `order`.platform_freight_code,
        `order`.customer_expected_freight_name,
        `order`.customer_expected_freight_code,
        `order`.trade_total,
        `order`.buyer_trade_total,
        `order`.buyer_unpaid_total,
        `order`.dised_taxed_total,
        `order`.ptype_preferential_total,
        `order`.tax_total,
        `order`.total,
        `order`.order_buyer_freight_fee,
        `order`.ptype_service_fee,
        `order`.mapping_state,
        `order`.order_deliver_required,
        `order`.buyer_id,
        `order`.btype_id,
        `order`.ktype_id,
        `order`.seller_memo,
        `order`.buyer_message,
        `order`.remark,
        `order`.pay_no,
        `order`.dtype_id,
        `order`.process_state,
        `order`.unique_mark,
        `order`.deleted,
        `order`.local_refund_state,
        `order`.order_sale_type,
        `order`.self_delivery_mode,
        `order`.salesman,
        `order`.summary,
        `order`.platform_ptype_preferential_total,
        `order`.order_preferential_allot_total,
        `order`.buyer_paid_total,
        `order`.ptype_commission_total,
        if(`order`.distribution_dised_taxed_total = 0,`order`.dised_taxed_total,`order`.distribution_dised_taxed_total)
        as distribution_dised_taxed_total,
        `order`.pay_btype_id,
        `order`.commission_btype_id,
        `order`.platform_stock_id,
        `order`.platform_stock_code,
        `order`.mode_of_payment,
        `order`.merchant_payment_account,
        `order`.payway_id,
        `order`.platform_parent_order_id,
        `order`.re_send_state,
        `order`.sender_id as 'senderId',
        `order`.deliver_process_type,
        `smap`.platform_store_name AS 'platform_stock_name',
        `timing`.eshop_order_id AS 'timing.eshopOrderId',
        `timing`.profile_id AS 'timing.profile_id',
        `timing`.cn_service AS 'timing.cn_service',
        `timing`.plan_send_time AS 'timing.plan_send_time',
        `timing`.plan_sign_time AS 'timing.plan_sign_time',
        `timing`.send_time AS 'timing.send_time',
        `timing`.promised_collect_time AS 'timing.promisedCollectTime',
        `timing`.promised_sign_time AS 'timing.promisedSignTime',
        `timing`.sign_time AS 'timing.signTime',
        `timing`.system_timing AS 'timing.system_timing',
        `timing`.create_time AS 'timing.create_time',
        `timing`.update_time AS 'timing.update_time',
        `timing`.timing_type AS 'timing.timing_type',
        `timing`.promised_sync_freight_time AS 'timing.promisedSyncFreightTime',
        `timing`.promised_sign_startTime AS 'timing.promised_sign_startTime',
        `invoice`.id AS 'invoiceInfo.id',
        `invoice`.profile_id AS 'invoiceInfo.profile_id',
        `invoice`.eshop_order_id AS 'invoiceInfo.eshopOrderId',
        `invoice`.invoice_required AS 'invoiceInfo.invoice_required',
        `invoice`.invoice_type AS 'invoiceInfo.invoice_type',
        `invoice`.invoice_total AS 'invoiceInfo.invoice_total',
        `invoice`.invoice_state AS 'invoiceInfo.invoice_state',
        `invoice`.invoice_title AS 'invoiceInfo.invoice_title',
        `invoice`.invoice_code AS 'invoiceInfo.invoice_code',
        `invoice`.invoice_special_info AS 'invoiceInfo.invoice_special_info',
        `invoice`.invoice_remark AS 'invoiceInfo.invoice_remark',
        `invoice`.invoice_category AS 'invoiceInfo.invoice_category',
        `invoice`.create_time AS 'invoiceInfo.create_time',
        `invoice`.update_time AS 'invoiceInfo.create_time',
        `invoice`.invoice_company AS 'invoiceInfo.invoice_company',
        `invoice`.invoice_register_addr AS 'invoiceInfo.invoice_register_addr',
        `invoice`.invoice_register_phone AS 'invoiceInfo.invoice_register_phone',
        `invoice`.invoice_bank AS 'invoiceInfo.invoice_bank',
        `invoice`.invoice_bank_account AS 'invoiceInfo.invoice_bank_account',
        `invoice`.secret_id AS 'invoiceInfo.secret_id',
        `extend`.eshop_order_id AS 'extend.eshopOrderId',
        `extend`.export_count AS 'extend.export_count',
        `extend`.order_number AS 'extend.order_number',
        `extend`.otype_id AS 'extend.otype_id',
        `extend`.profile_id AS 'extend.profile_id',
        `extend`.create_time AS 'extend.create_time',
        `extend`.update_time AS 'extend.update_time',
        `extend`.deliver_send_state AS 'extend.deliver_send_state',
        `extend`.Installation_service_provider AS 'extend.Installation_service_provider',
        `extend`.collect_time AS 'extend.collect_time',
        `extend`.collect_customer AS 'extend.collect_customer',
        `extend`.gather_status AS 'extend.gather_status',
        `extend`.payment_mode AS 'extend.payment_mode',
        `extend`.mark_info AS 'extend.mark_info',
        `extend`.hold_time AS 'extend.hold_time',
        `extend`.platform_required AS 'extend.platform_required',
        `extend`.platform_json AS 'extend.platform_json',
        `extend`.group_header_name AS 'extend.group_header_name',
        `extend`.supplier_id AS 'extend.supplierId',
        `extend`.purchase_total AS 'extend.purchaseTotal',
        `extend`.advance_total AS 'extend.advanceTotal',
        `extend`.pickup_code AS 'extend.pickup_code',
        `extend`.platform_dispatcher_name AS 'extend.platform_dispatcher_name',
        `extend`.platform_dispather_mobile AS 'extend.platform_dispather_mobile',
        `extend`.logistics_status AS 'extend.logistics_status',
        `extend`.confirm_status AS 'extend.confirm_status',
        `extend`.real_buyer_id AS 'extend.real_buyer_id',
        `extend`.pick_up_address_id AS 'extend.pick_up_address_id',
        `extend`.platform_qc_result AS 'extend.platform_qc_result',
        `extend`.platform_identify_result AS 'extend.platform_identify_result',
        `extend`.flow_channel AS 'extend.flowChannel',
        `extend`.mall_deduction_fee AS 'extend.mall_deduction_fee',
        `extend`.seller_flag_memo AS 'extend.seller_flag_memo',
        `extend`.group_title AS 'extend.group_title',
        `extend`.group_no AS 'extend.group_no',
        `extend`.take_goods_code AS 'extend.take_goods_code',
        `extend`.platform_quality_org_id AS 'extend.platform_quality_org_id',
        `extend`.platform_quality_org_name AS 'extend.platform_quality_org_name',
        `extend`.platform_quality_warehouse_code AS 'extend.platform_quality_warehouse_code',
        `extend`.platform_quality_warehouse_name AS 'extend.platform_quality_warehouse_name',
        `extend`.anchor_order_preferential_total AS 'extend.anchor_order_preferential_total',
        `extend`.platform_order_subsidy_total AS 'extend.platform_order_subsidy_total',
        `extend`.sale_period_num AS 'extend.sale_period_num',
        `extend`.current_period_num AS 'extend.current_period_num',
        `extend`.national_subsidy_total AS 'extend.national_subsidy_total',
        `extend`.gathered_total AS 'extend.gathered_total',
        `config`.process_type,
        `eshop`.eshop_type as 'shopType',
        `distribution`.profile_id as 'distributionBuyer.profileId',
        `distribution`.distribution_buyer_trade_id as 'distribution.distribution_buyer_trade_id',
        `distribution`.eshop_order_id as 'distribution.eshop_order_id',
        `distribution`.create_time as 'distribution.create_time',
        `distribution`.update_time as 'distribution.update_time',
        `qic`.id as 'extend.qicConfig.id',
        `qic`.profile_id as 'extend.qicConfig.profile_id',
        `qic`.eshop_id as 'extend.qicConfig.eshop_id',
        `qic`.update_time as 'extend.qicConfig.update_time',
        `qic`.create_time as 'extend.qicConfig.create_time',
        `qic`.platform_quality_org_id as 'extend.qicConfig.platform_quality_org_id',
        `qic`.platform_quality_org_name as 'extend.qicConfig.platform_quality_org_name',
        `qic`.platform_quality_warehouse_code as 'extend.qicConfig.platform_quality_warehouse_code',
        `qic`.platform_quality_warehouse_name as 'extend.qicConfig.platform_quality_warehouse_name',
        `qic`.platform_quality_status as 'extend.qicConfig.platform_quality_status',
        `qic`.platform_quality_refund_interception_code as 'extend.qicConfig.platform_quality_refund_interception_code',
        `qic`.platform_quality_receive_address as 'extend.qicConfig.platform_quality_receive_address',
        `qic`.platform_quality_btype_code as 'extend.qicConfig.platform_quality_btype_code',
        `qic`.platform_quality_btype_name as 'extend.qicConfig.platform_quality_btype_name',
        `qic`.platform_quality_btype_insure as 'extend.qicConfig.platform_quality_btype_insure',
        `qic`.platform_quality_btype_insure_total as 'extend.qicConfig.platform_quality_btype_insure_total',
        `qic`.platform_quality_btype_insure_type as 'extend.qicConfig.platform_quality_btype_insure_type',
        `qic`.platform_quality_btype_backup_code as 'extend.qicConfig.platformQualityBtypeCodeBackup',
        `qic`.platform_quality_btype_backup_name as 'extend.qicConfig.platformQualityBtypeNameBackup',
        `qic`.platform_quality_btype_backup_insure as 'extend.qicConfig.platform_quality_btype_backup_insure',
        `qic`.platform_quality_btype_backup_insure_total as
        'extend.qicConfig.platform_quality_btype_backup_insure_total',
        `qic`.platform_quality_btype_backup_insure_type as 'extend.qicConfig.platform_quality_btype_backup_insure_type',
        `qic`.platform_quality_comment as 'extend.qicConfig.platform_quality_comment',
        `qic`.platform_quality_btype_product as 'extend.qicConfig.platform_quality_btype_product',
        `qic`.platform_quality_btype_backup_product as 'extend.qicConfig.platformQualityBtypeBackupProduct'
        FROM ${tableName}
        LEFT JOIN pl_eshop_sale_order_timing timing ON timing.profile_id=`order`.profile_id AND
        timing.eshop_order_id=`order`.id
        LEFT JOIN pl_eshop_sale_order_invoice invoice ON invoice.profile_id=`order`.profile_id AND
        invoice.eshop_order_id=`order`.id
        LEFT JOIN pl_eshop_sale_order_extend extend ON extend.profile_id=`order`.profile_id AND
        extend.eshop_order_id=`order`.id
        LEFT JOIN pl_eshop_sale_order_distribution_buyer distribution ON distribution.profile_id=`order`.profile_id AND
        distribution.eshop_order_id=`order`.id
        LEFT JOIN pl_eshop_platform_store_mapping smap ON smap.platform_store_stock_id = `order`.platform_stock_id AND
        smap.eshop_id = `order`.otype_id AND
        smap.profile_id = `order`.profile_id
        LEFT JOIN pl_eshop eshop ON eshop.otype_id = `order`.otype_id AND
        eshop.profile_id = `order`.profile_id
        LEFT JOIN pl_eshop_config config ON config.eshop_id = `order`.otype_id AND
        config.profile_id = `order`.profile_id
        LEFT JOIN pl_eshop_qic_config qic ON qic.eshop_id = `order`.otype_id AND
        qic.profile_id = `order`.profile_id AND extend.platform_qc_result=5 AND config.platform_quality_status=1
        WHERE `order`.profile_id=#{profileId} AND `order`.ktype_id != 0 AND `order`.order_sale_type != 6
        <if test="businessTypes!=null and businessTypes.size()>0">
            AND `order`.business_type IN
            <foreach collection="businessTypes" close=")" open="(" separator="," item="businessType">
                #{businessType}
            </foreach>
        </if>
        <if test="eshopOrderId!='' and eshopOrderId!=null">
            AND `order`.id=#{eshopOrderId}
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `order`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="tradeStatus !=null and tradeStatus.size() > 0 ">
            and ( `order`.local_trade_state in
            <foreach collection="tradeStatus" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
            <if test="deliverSendState !=null and  deliverSendState==@com.wsgjp.ct.sale.biz.eshoporder.api.enums.ProcessStatusEnum@SENDED">
                OR `extend`.deliver_Send_State=35
            </if>
                OR (`order`.deliver_process_type = 0 AND `order`.local_trade_state IN (3,4))
            )
        </if>
        <if test="refundStates !=null and refundStates.size() > 0 ">
            and `order`.local_refund_state in
            <foreach collection="refundStates" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND `order`.mapping_state=1
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND `order`.mapping_state=0
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@Submit">
            AND `order`.process_state=1
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@NoSubmit">
            AND `order`.process_state in (0,2)
            AND `order`.order_deliver_required=1
            AND `order`.local_trade_state!=5
        </if>
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            AND `order`.id IN
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="deleted != null ">
            AND `order`.deleted= #{deleted}
        </if>
        <if test="isDraft !=null and isDraft.size()>0 ">
            AND `extend`.is_draft IN
            <foreach collection="isDraft" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="timeType != null and timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@TRADE_CREATE_TIME">
            AND `order`.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType != null and timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@PAY_TIME">
            AND `order`.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType != null and timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@DOWNLOAD_TIME">
            AND `order`.create_time between #{beginTime} and #{endTime}
        </if>

        <if test="autoSubmitQuery!=null and autoSubmitQuery==true">
            AND `extend`.hold_time <![CDATA[<]]> #{currentTime}
        </if>

        <if test="enableCycleOrderSubmit!=null and enableCycleOrderSubmit==false">
            AND `order`.order_sale_type != 3
        </if>

        <if test="platformParentOrderIds !=null and platformParentOrderIds.size()>0 ">
            and `order`.platform_parent_order_id in
            <foreach collection="platformParentOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>

        <choose>
            <when test="timeType != null and timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@PAY_TIME">
                order by `order`.trade_pay_time desc limit #{submitOrderCount}
            </when>
            <otherwise>
                order by `order`.trade_pay_time+0 desc limit #{submitOrderCount}
            </otherwise>
        </choose>
    </select>

    <select id="queryAllSaleOrderDetailFields"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail">
        select
        `d`.*,
        `distribution`.profile_id AS 'distribution.profile_id',
        `distribution`.eshop_order_id AS 'distribution.eshopOrderId',
        `distribution`.eshop_order_detail_id AS 'distribution.eshop_order_detail_id',
        `distribution`.eshop_id AS 'distribution.eshop_id',
        `distribution`.buyer_price AS 'distribution.buyer_price',
        `distribution`.buyer_total AS 'distribution.buyer_total',
        `distribution`.buyer_discount AS 'distribution.buyer_discount',
        `distribution`.buyer_dised_initial_price AS 'distribution.buyer_dised_initial_price',
        `distribution`.buyer_dised_initial_total AS 'distribution.buyer_dised_initial_total',
        `distribution`.buyer_ptype_preferential_total AS 'distribution.buyer_ptype_preferential_total',
        `distribution`.buyer_order_preferential_allot_total AS 'distribution.buyer_order_preferential_allot_total',
        IF( `order`.business_type != 203,d.dised_taxed_price, `distribution`.buyer_dised_taxed_price) AS
        'distribution.buyer_dised_taxed_price',
        IF( `order`.business_type != 203,d.dised_taxed_total, `distribution`.buyer_dised_taxed_total) AS
        'distribution.buyer_dised_taxed_total',
        `distribution`.buyer_tax_rate AS 'distribution.buyer_tax_rate',
        `distribution`.buyer_tax_total AS 'distribution.buyer_tax_total',
        `distribution`.buyer_dised_price AS 'distribution.buyer_dised_price',
        `distribution`.buyer_dised_total AS 'distribution.buyer_dised_total',
        `distribution`.update_time AS 'distribution.update_time',
        `distribution`.create_time AS 'distribution.create_time',
        `timing`.profile_id AS 'timing.profile_id',
        `timing`.eshop_order_id AS 'timing.eshop_order_id',
        `timing`.eshop_order_detail_id AS 'timing.eshop_order_detail_id',
        `timing`.id AS 'timing.id',
        `timing`.promised_send_time AS 'timing.promised_send_time',
        `unit`.unit_name as unitName,
        `unit`.unit_rate as unitRate,
        detailExtend.id as 'extend.id',
        detailExtend.profile_id as 'extend.profileId',
        detailExtend.otype_id as 'extend.otypeId',
        detailExtend.eshop_order_id as 'extend.eshopOrderId',
        detailExtend.eshop_order_detail_id as 'extend.detailId',
        detailExtend.mall_deduction_rate as 'extend.mallDeductionRate',
        detailExtend.mall_deduction_fee as 'extend.mallDeductionFee',
        detailExtend.gift as 'extend.gift',
        detailExtend.national_subsidy_total as 'extend.nationalSubsidyTotal',
        detailExtend.anchor_order_preferential_total as 'extend.anchorOrderPreferentialTotal',
        detailExtend.anchor_ptype_preferential_total as 'extend.anchorPtypePreferentialTotal',
        detailExtend.platform_order_subsidy_total as 'extend.platformOrderSubsidyTotal',
        detailExtend.platform_ptype_subsidy_total as 'extend.platformPtypeSubsidyTotal'
        from ${tableName}
        left join pl_eshop_sale_order_detail_distribution_buyer distribution ON d.id =distribution.eshop_order_detail_id
        AND d.profile_id=distribution.profile_id AND d.eshop_order_id=distribution.eshop_order_id
        left join pl_eshop_sale_order_detail_timing timing ON d.id =timing.eshop_order_detail_id AND
        d.profile_id=timing.profile_id AND d.eshop_order_id=timing.eshop_order_id
        left join base_ptype_unit unit on unit.profile_id=d.profile_id and unit.id=d.unit
        left join pl_eshop_sale_order_detail_extend detailExtend on d.profile_id = detailExtend.profile_id and
        d.otype_id = detailExtend.otype_id and d.id = detailExtend.eshop_order_detail_id
        and d.eshop_order_id = detailExtend.eshop_order_id
        LEFT JOIN pl_eshop_sale_order `order` on d.profile_id = `order`.profile_id and d.otype_id = `order`.otype_id and
        d.eshop_order_id = `order`.id
        where d.profile_id=#{profileId}
        <if test="eshopOrderId!=null">
            And d.eshop_order_id=#{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            And d.eshop_order_id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND d.mapping_state=0
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND d.mapping_state=1
        </if>
        <if test="waitAudit==true">
            AND d.mapping_state=1 AND d.local_refund_state!=4 AND d.platform_detail_trade_state!=2 and
            d.order_deliver_required=1 and d.ptype_id>0
        </if>
        <if test="waitSendAudit==true">
            AND d.mapping_state=1 AND d.platform_detail_trade_state!=2 and d.order_deliver_required=1 and d.ptype_id>0
        </if>
        <if test="tradeOrderDetailIds!=null and tradeOrderDetailIds.size>0">
            And d.trade_order_detail_id IN
            <foreach collection="tradeOrderDetailIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        ORDER BY d.combo_row_id ASC
    </select>

    <select id="queryAllSaleOrderDetailFreightList"
            resultType="com.wsgjp.ct.sale.platform.dto.order.entity.EshopOrderDetailFreight">
        select id, trade_order_id, profile_id, otype_id, trade_order_detail_id,
               freight_name, freight_bill_no,
               extend_info, create_type, send_qty, sync_state, sync_type, has_last,
               hash_key, freight_code, create_time, update_time
        from pl_eshop_order_detail_freight
        where profile_id=#{profileId} and otype_id=#{eshopId}
        <if test="tradeOrderIdList!=null and tradeOrderIdList.size>0">
            And trade_order_id IN
            <foreach collection="tradeOrderIdList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="tradeOrderDetailIds!=null and tradeOrderDetailIds.size>0">
            And trade_order_detail_id IN
            <foreach collection="tradeOrderDetailIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>


    <select id="queryAllSaleOrderComboFields"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleDetailCombo">
        select combo.*,
        `distribution`.profile_id AS 'distribution.profile_id',
        `distribution`.eshop_order_combo_row_id AS 'distribution.eshop_order_combo_row_id',
        `distribution`.eshop_order_id AS 'distribution.eshopOrderId',
        `distribution`.combo_id AS 'distribution.combo_id',
        `distribution`.trade_order_detail_id AS 'distribution.trade_order_detail_id',
        `distribution`.buyer_price AS 'distribution.buyer_price',
        `distribution`.buyer_total AS 'distribution.buyer_total',
        `distribution`.buyer_discount AS 'distribution.buyer_discount',
        `distribution`.buyer_dised_initial_price AS 'distribution.buyer_dised_initial_price',
        `distribution`.buyer_dised_initial_total AS 'distribution.buyer_dised_initial_total',
        `distribution`.buyer_ptype_preferential_total AS 'distribution.buyer_ptype_preferential_total',
        `distribution`.buyer_order_preferential_allot_total AS 'distribution.buyer_order_preferential_allot_total',
        IF( `order`.business_type != 203, combo.dised_taxed_price, `distribution`.buyer_dised_taxed_price) AS
        'distribution.buyer_dised_taxed_price',
        IF( `order`.business_type != 203, combo.dised_taxed_total, `distribution`.buyer_dised_taxed_total) AS
        'distribution.buyer_dised_taxed_total',
        `distribution`.buyer_tax_rate AS 'distribution.buyer_tax_rate',
        `distribution`.buyer_tax_total AS 'distribution.buyer_tax_total',
        `distribution`.buyer_dised_price AS 'distribution.buyer_dised_price',
        `distribution`.buyer_dised_total AS 'distribution.buyer_dised_total',
        `distribution`.update_time AS 'distribution.update_time',
        `distribution`.create_time AS 'distribution.create_time'
        from pl_eshop_sale_order_detail_combo combo
        left join pl_eshop_sale_order_detail_combo_distribution_buyer distribution on distribution.profile_id=
        combo.profile_id
        and distribution.eshop_order_combo_row_id = combo.eshop_order_detail_combo_row_id
        and distribution.eshop_order_id = combo.eshop_order_id
        LEFT JOIN pl_eshop_sale_order `order` on combo.profile_id = `order`.profile_id and combo.eshop_order_id =
        `order`.id
        where combo.profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=''">
            and combo.eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND combo.eshop_order_id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="eshopOrderId">
                #{eshopOrderId}
            </foreach>
        </if>
    </select>
    <select id="querySimpleAdvanceOrderList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity">
        SELECT `core`.vchcode,
        `core`.order_sale_type,
        `core`.deleted,
        `core`.submit_send_state as 'submit_send_state',
        `core`.buyer_id,
        `core`.profile_id,
        `core`.otype_id as 'otype_id',
        `platform`.local_trade_state as 'platform.local_trade_state',
        `platform`.local_refund_state as 'platform.local_refund_state',
        `platform`.profile_id as 'platform.profile_id',
        `platform`.trade_id as 'platform.trade_id',
        `platform`.seller_memo as 'platform.seller_memo',
        `platform`.seller_flag as 'platform.seller_flag',
        `platform`.submit_batch_id AS 'platform.submit_batch_id',
        `platform`.local_freight_name as 'platform.local_freight_name',
        `platform`.local_freight_code as 'platform.local_freight_code',
        `platform`.local_freight_bill_no AS 'platform.local_freight_bill_no',
        `platform`.buyer_message AS 'platform.buyer_message',
        `platform`.trade_finish_time AS 'platform.trade_finish_time',
        `platform`.trade_pay_time AS 'platform.trade_pay_time',
        `platform`.re_send_state AS 'platform.re_send_state',
        `timing`.plan_send_time AS 'timing.plan_send_time',
        `platformExtend`.seller_flag_memo as 'platform.platformExtend.seller_flag_memo'
        FROM td_orderbill_core `core`
        LEFT JOIN td_orderbill_timing timing ON timing.profile_id=`core`.profile_id AND timing.vchcode=`core`.vchcode
        LEFT JOIN td_orderbill_platform platform ON platform.profile_id=`core`.profile_id AND
        platform.vchcode=`core`.vchcode
        LEFT JOIN td_orderbill_platform_extend platformExtend ON platformExtend.profile_id=`core`.profile_id AND
        platformExtend.vchcode=`core`.vchcode
        where core.profile_id = #{profileId}
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND core.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size() > 0">
            AND platform.trade_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
    </select>
    <update id="modifySaleOrderExpectedFreight"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        update pl_eshop_sale_order
        <set>
            <if test="orderEntity.customerExpectedFreightName!=null">
                customer_expected_freight_name=#{orderEntity.customerExpectedFreightName},
            </if>
            <if test="orderEntity.customerExpectedFreightCode!=null">
                customer_expected_freight_code=#{orderEntity.customerExpectedFreightCode},
            </if>1
        </set>
        where profile_id = #{orderEntity.profileId}
        and otype_id = #{orderEntity.otypeId}
        and trade_order_id = #{orderEntity.tradeId}
    </update>
    <select id="querySimpleAdvanceDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetail">
        select d.*,
        dp.stock_sync_rule_id as 'platform.stock_sync_rule_id',
        dp.trade_order_detail_id as 'platform.trade_order_detail_id',
        da.unit_qty as 'assinfo.unit_qty',
        dp.process_state as 'platform.process_state',
        dp.platform_detail_trade_state as 'platform.platform_detail_trade_state',
        dp.deliver_required as 'platform.deliver_required',
        dp.local_refund_process_state as 'platform.local_refund_process_state',
        dp.local_refund_state as 'platform.local_refund_state',
        dp.re_send_state as 'platform.re_send_state'
        from td_orderbill_detail_core d
        left join td_orderbill_detail_platform dp on dp.profile_id=d.profile_id and dp.detail_id=d.detail_id
        left join td_orderbill_detail_assinfo da on da.profile_id=d.profile_id and da.detail_id=d.detail_id
        where d.profile_id = #{profileId}
        <if test="eshopOrderId!=null">
            and d.vchcode=#{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            and d.vchcode in
            <foreach collection="eshopOrderIds" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="batchQueryOrderSubmitBatchDetail"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.OrderSubmitBatchDetailEntity">
        select *
        from pl_eshop_submit_batch_detail
        where profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="eshopOrderId">
                #{eshopOrderId}
            </foreach>
        </if>
    </select>

    <select id="queryOrderSubmitBatchDetail"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.OrderSubmitBatchDetailEntity">
        select *
        from pl_eshop_submit_batch_detail
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=''">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="submitBatchIds!=null and submitBatchIds.size()>0">
            AND submit_batch_id IN
            <foreach collection="submitBatchIds" close=")" open="(" separator="," item="batchId">
                #{batchId}
            </foreach>
        </if>
    </select>

    <select id="querySimpleComboRow"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleDetailCombo">
        select combo.*,combo_distribution.buyer_dised_taxed_price as distributionDisedTaxedPrice,
        combo_distribution.buyer_dised_taxed_total distributionDisedTaxedTotal
        ,combo.platform_order_preferential_total,combo.platform_order_subsidy_total
        from pl_eshop_sale_order_detail_combo combo
        LEFT JOIN pl_eshop_sale_order_detail_combo_distribution_buyer combo_distribution on
        combo_distribution.profile_id = combo.profile_id
        and combo_distribution.eshop_order_combo_row_id = combo.eshop_order_detail_combo_row_id
        where combo.profile_id = #{profileId}
        <if test="eshopOrderId!=null">
            and combo.eshop_order_id=#{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            and combo.eshop_order_id in
            <foreach collection="eshopOrderIds" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </select>


    <select id="queryComboRowForRefund" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleDetailCombo">
        select apply_refund_taxed_total as disedTaxedTotal
        from pl_eshop_refund_apply_detail_combo
        where profile_id = #{profileId}
        <if test="comboRowIds!=null and comboRowIds.size()>0">
            and id in
            <foreach collection="comboRowIds" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="queryComboRowForOrder" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleDetailCombo">
        select *
        from pl_eshop_sale_order_detail_combo
        where profile_id = #{profileId}
        <if test="comboRowIds!=null and comboRowIds.size()>0">
            and eshop_order_detail_combo_row_id in
            <foreach collection="comboRowIds" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </select>


    <select id="queryAdvanceSimpleComboRow"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetailCombo">
        select *
        from td_orderbill_detail_combo
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null">
            and vchcode=#{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            and vchcode in
            <foreach collection="eshopOrderIds" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </select>
    <update id="modifySaleOrderBuyerid">
        update pl_eshop_sale_order
        set buyer_id=#{buyerId}
        where profile_id = #{profileId}
          and otype_id = #{otypeId}
          and trade_order_id = #{tradeOrderId}
    </update>

    <insert id="insertEshopOrderMark" parameterType="java.util.List">
        replace into pl_eshop_order_mark
        (detail_id, order_id, profile_id,
        order_type,mark_target,mark_code,id,bubble,show_type,mark_use_type,create_type,mark_data_id,reason,uniqueId)
        values
        <foreach collection="markList" item="itemMark" index="index" separator=",">
            (
            #{itemMark.detailId},#{itemMark.orderId},#{itemMark.profileId},#{itemMark.orderType},
            #{itemMark.markTarget},#{itemMark.markCode},#{itemMark.id},#{itemMark.bubble},#{itemMark.showType},#{itemMark.markUseType},#{itemMark.createType},#{itemMark.markDataId},#{itemMark.reason},#{itemMark.uniqueId}
            )
        </foreach>
    </insert>
    <insert id="insertEshopOrderMarkData">
        insert ignore into mark_data
        (id, profile_id, big_data)
        values
        <foreach collection="markList" item="itemMark" index="index" separator=",">
            (
            #{itemMark.markDataId},#{itemMark.profileId},#{itemMark.bigData}
            )
        </foreach>
    </insert>

    <delete id="deleteEshopOrderMark">
        delete
        from pl_eshop_order_mark where profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            AND order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="markCodes!=null and markCodes.size>0">
            AND mark_code IN
            <foreach collection="markCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="noDeleteMarkCodes!=null and noDeleteMarkCodes.size>0">
            AND mark_code NOT IN
            <foreach collection="noDeleteMarkCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="markTarget != null and markTarget == 0">
            AND mark_target=0
        </if>
        <if test="markTarget != null and markTarget == 1">
            AND mark_target=1
        </if>
        <if test="createType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MarkCreateType@DownloadSysCalc">
            AND create_type=0
        </if>
        <if test="createType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MarkCreateType@ManualMark">
            AND create_type=1
        </if>
        <if test="createType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MarkCreateType@SystemCalc">
            AND create_type=2
        </if>
    </delete>
    <delete id="deleteEshopOrderMarkData">
        delete
        from mark_data where profile_id = #{profileId}
        <if test="markDataIds!=null and markDataIds.size>0">
            AND id IN
            <foreach collection="markDataIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <select id="queryEshopOrderMarkCount"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_order_mark
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=''">
            and order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            AND order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="markCodes!=null and markCodes.size>0">
            AND mark_code IN
            <foreach collection="markCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="markTarget != null and markTarget == 0">
            AND mark_target=0
        </if>
        <if test="markTarget != null and markTarget == 1">
            AND mark_target=1
        </if>
    </select>
    <select id="queryEshopOrderMark"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopOrderMarkEntity">
        select
        m.detail_id,
        m.profile_id,
        m.order_type,
        m.mark_code,
        m.id,
        m.bubble,
        m.create_time,
        m.update_time,
        m.show_type,
        m.order_id,
        m.mark_target,
        m.mark_use_type,
        m.create_type,
        m.mark_data_id,
        m.reason,
        m.uniqueId,d.id as markDataId,d.big_data as bigData
        from pl_eshop_order_mark m
        left join mark_data d on d.id=m.mark_data_id and d.profile_id=m.profile_id
        where m.profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=''">
            and m.order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            AND m.order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="markCodes!=null and markCodes.size>0">
            AND m.mark_code IN
            <foreach collection="markCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="markTarget != null and markTarget == 0">
            AND m.mark_target=0
        </if>
        <if test="markTarget != null and markTarget == 1">
            AND m.mark_target=1
        </if>
        <if test="orderType != null">
            AND m.order_type = #{orderType}
        </if>
    </select>

    <select id="queryEshopOrderMarkByTradeOrderId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopOrderMarkEntity">
        select
        m.detail_id,
        m.profile_id,
        m.order_type,
        m.mark_code,
        m.id,
        m.bubble,
        m.create_time,
        m.update_time,
        m.show_type,
        m.order_id,
        m.mark_target,
        m.mark_use_type,
        m.create_type,
        m.mark_data_id,
        m.reason,
        m.uniqueId,d.id as markDataId,d.big_data as bigData
        from pl_eshop_sale_order o
        join pl_eshop_order_mark m on o.profile_id=m.profile_id and o.id=m.order_id
        left join mark_data d on d.id=m.mark_data_id and d.profile_id=m.profile_id
        where o.profile_id = #{profileId}
            and o.otype_id = #{otypeId}
            AND o.trade_order_id = #{tradeOrderId}
            AND m.mark_target=0
        <if test="markCodes!=null and markCodes.size>0">
            AND m.mark_code IN
            <foreach collection="markCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryEshopOrderMarkByLimit"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopOrderMarkEntity">
        select m.detail_id,
        m.profile_id,
        m.order_type,
        m.mark_code,
        m.id,
        m.bubble,
        m.create_time,
        m.update_time,
        m.show_type,
        m.order_id,
        m.mark_target,
        m.mark_use_type,
        m.create_type,
        m.mark_data_id,
        m.reason,
        m.uniqueId,d.id as markDataId,d.big_data as bigData
        from pl_eshop_order_mark m
        left join mark_data d on d.id=m.mark_data_id and d.profile_id=m.profile_id
        where m.profile_id = #{parameter.profileId}
        <if test="parameter.eshopOrderId!=null and parameter.eshopOrderId!=''">
            and m.order_id = #{parameter.eshopOrderId}
        </if>
        <if test="parameter.eshopOrderIds!=null and parameter.eshopOrderIds.size>0">
            AND m.order_id IN
            <foreach collection="parameter.eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="parameter.markCodes!=null and parameter.markCodes.size>0">
            AND m.mark_code IN
            <foreach collection="parameter.markCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="parameter.markTarget != null and parameter.markTarget == 0">
            AND m.mark_target=0
        </if>
        <if test="parameter.markTarget != null and parameter.markTarget == 1">
            AND m.mark_target=1
        </if>
        limit #{pageNum}, #{pageSize}
    </select>

    <select id="querySaleOrderVchcode" resultType="java.math.BigInteger">
        select id
        from pl_eshop_sale_order
        where profile_id = #{profileId}
          and trade_order_id = #{tradeOrderId}
          and otype_id = #{otypeId}
    </select>


    <select id="getOrderProcessState" resultType="java.lang.Integer">
        select process_state
        from pl_eshop_sale_order
        where id = #{arg0}
          and profile_id = #{arg1}
    </select>
    <select id="getPostState" resultType="java.lang.Integer">
        select post_state
        from acc_bill_core
        where vchcode in
              (select distinct vchcode from td_bill_detail_deliver where order_id = #{arg0} and profile_id = #{arg1})
          and profile_id = #{arg1}
    </select>
    <select id="querySaleOrderListByExceptionStatus" resultType="java.math.BigInteger">
        SELECT
        `order`.id
        FROM ${tableName}
        <include refid="order_jointable"/>
        WHERE `order`.profile_id=#{profileId}
        <if test="pageFrom=='refund'">
            and otype.ocategory!=2
        </if>
        <if test="eshopOrderIds !=null and eshopOrderIds.size()>0">
            AND `order`.id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="eshopOrderId!='' and eshopOrderId !=null">
            AND `order`.id=#{id}
        </if>
        <if test="allowOtypeIds!=null and allowOtypeIds.size()>0 and otypeLimited">
            and `order`.otype_id in
            <foreach collection="allowOtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowKtypeIds!=null and allowKtypeIds.size()>0 and ktypeLimited">
            and `order`.Ktype_id in
            <foreach collection="allowKtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowEtypeIds!=null and allowEtypeIds.size()>0 and etypeLimited">
            and `order`.etype_id in
            <foreach collection="allowEtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND trade_order_id IN
            <foreach collection="tradeIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
        <if test="customerAreas !=null and customerAreas.size() > 0 ">
            and
            <foreach collection="customerAreas" close=")" open="(" separator="or" item="area">
                (`buyer`.customer_receiver_province=#{area.customerReceiverProvince}
                <if test="area.customerReceiverCity != null and area.customerReceiverCity != ''">
                    and `buyer`.customer_receiver_city=#{area.customerReceiverCity}
                </if>
                <if test="area.customerReceiverDistrict != null and area.customerReceiverDistrict != ''">
                    and `buyer`.customer_receiver_district=#{area.customerReceiverDistrict}
                </if>
                )
            </foreach>
        </if>

        <include refid="order_quick_filter_key_type"/>
        <include refid="order_quick_filter_key_type_new"/>

        <include refid="order_query_new"/>

        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@TRADE_CREATE_TIME">
            AND `order`.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@PAY_TIME">
            AND `order`.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@DOWNLOAD_TIME">
            AND `order`.create_time between #{beginTime} and #{endTime}
        </if>
        <if test="intSellerFlag !=null and intSellerFlag>0">
            AND `order`.seller_flag=#{intSellerFlag}
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND `order`.mapping_state=1
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND `order`.mapping_state=0
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@Submit">
            AND `order`.process_state=1 AND `order`.mapping_state=1
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@NoSubmit">
            AND `order`.process_state=0
            AND `order`.mapping_state=1 AND `order`.deliver_required=1
            AND `order`.local_trade_state!=5 AND `order`.deliver_required=1
        </if>
        <if test="quickFilterTypes !=null and quickFilterTypes.size() > 0 ">
            AND ( 1!=1
            <foreach collection="quickFilterTypes" close="" open="" separator="" item="quickFilterType">
                <include refid="order_quick_filter_or"/>
            </foreach>
            )
        </if>
        <if test="filterParameter != null">
            <include refid="order_quick_filter"/>
        </if>
        <if test="btypeIds!=null and btypeIds.size() > 0">
            AND `order`.btype_id IN
            <foreach collection="btypeIds" close=")" open="(" separator="," item="btypeId">
                #{btypeId}
            </foreach>
        </if>
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            AND `order`.id IN
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="deleted != null ">
            AND `order`.deleted= #{deleted}
        </if>
        <if test="filterParameter != null">
            <include refid="order_filter"/>
        </if>
        <if test="filterParameter == null or (filterParameter != null and filterParameter.defaultSort)">
            order by `order`.trade_pay_time+0 desc
        </if>
    </select>
    <select id="findEshopOrderDisedTaxedTotal" resultType="java.math.BigDecimal">
        select sum(o.dised_taxed_total)
        from (select if(peso.distribution_dised_taxed_total = 0, peso.dised_taxed_total,
        peso.distribution_dised_taxed_total) as dised_taxed_total
        from pl_eshop_sale_order peso
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=peso.profile_id and blsk.object_type=2 and
            peso.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=peso.profile_id and bls.object_type=3 and
            peso.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where date_sub(CURDATE(), INTERVAL 0 DAY) &lt;= peso.trade_create_time
        and peso.profile_id = #{profileId})
        as o
    </select>

    <select id="findEshopOrderHasRefund"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderResult">
        select count(DISTINCT(peso.id)) AS hasRefund
        FROM pl_eshop_sale_order peso
        JOIN pl_eshop_refund re ON re.profile_id = peso.profile_id  AND re.otype_id = peso.otype_id  AND re.trade_order_id = peso.trade_order_id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=peso.profile_id and blsk.object_type=2 and
            peso.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=peso.profile_id and bls.object_type=3 and
            peso.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where
        peso.profile_id = #{profileId} AND peso.deleted = 0
        AND date_sub(CURDATE(), INTERVAL 29 DAY) &lt;= peso.trade_create_time
        AND date_sub(CURDATE(), INTERVAL 29 DAY) &lt;= re.create_time
        AND re.deleted = 0
        AND re.refund_type IN (0,1) AND re.refund_state NOT IN (0,4,6)
    </select>

    <select id="findEshopOrderNoSubmitAll"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderResult">
        select count(distinct (peso.id)) as waitSubmit
        from pl_eshop_sale_order peso
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=peso.profile_id and blsk.object_type=2 and
            peso.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=peso.profile_id and bls.object_type=3 and
            peso.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where peso.profile_id = #{profileId}
        AND `peso`.process_state = 0
        AND `peso`.local_refund_state = 0
        AND `peso`.deleted = 0
        AND `peso`.local_trade_state IN (1, 2, 3, 4, 6, 7)
        AND `peso`.ktype_id != 0
        AND `peso`.mapping_state = 1
        AND `peso`.order_deliver_required = 1
        AND date_sub(CURDATE(), INTERVAL 29 DAY) &lt;= `peso`.trade_create_time
        AND (`peso`.order_sale_type  not in(3,6) OR (`peso`.order_sale_type=3 AND `peso`.platform_parent_order_id=''))
    </select>

    <select id="findEshopOrderNoSubmitAllNew"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderResult">
        select count(distinct (peso.id)) as waitSubmit
        from pl_eshop_sale_order peso
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=peso.profile_id and blsk.object_type=2 and
            peso.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=peso.profile_id and bls.object_type=3 and
            peso.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where peso.profile_id = #{profileId}
        AND `peso`.process_state = 0
        AND `peso`.local_refund_state IN (0,1,2)
        AND `peso`.deleted = 0
        AND `peso`.local_trade_state IN (0,2)
        AND `peso`.order_deliver_required = 1
        AND date_sub(CURDATE(), INTERVAL 29 DAY) &lt;= `peso`.trade_create_time
        AND (`peso`.order_sale_type  not in(3,6) OR (`peso`.order_sale_type=3 AND `peso`.platform_parent_order_id=''))
    </select>

    <select id="findEshopOrderUnRelation"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderResult">
        select count(distinct (peso.id)) as unRelation
        from pl_eshop_sale_order peso
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=peso.profile_id and blsk.object_type=2 and
            peso.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=peso.profile_id and bls.object_type=3 and
            peso.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where peso.profile_id = #{profileId}
        AND `peso`.mapping_state = 0
        AND `peso`.order_deliver_required = 1
        AND `peso`.local_trade_state != 5
        AND `peso`.deleted = 0
        AND date_sub(CURDATE(), INTERVAL 29 DAY) &lt;= `peso`.trade_create_time
        AND (`peso`.order_sale_type not in (3,6) OR (`peso`.order_sale_type=3 AND `peso`.platform_parent_order_id=''))
        <if test="insertUnRelationByNormalOrder">
            AND `peso`.local_refund_state !=4 AND not exists(select 1
            from pl_eshop_order_mark m
            where m.profile_id = `peso`.profile_id
            and m.mark_target = 0
            and m.order_id = `peso`.id
            and m.mark_code = 90870001)
        </if>
    </select>

    <select id="findEshopOrderNoKtype"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderResult">
        select count(distinct (peso.id)) as noKtype
        from pl_eshop_sale_order peso
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=peso.profile_id and blsk.object_type=2 and
            peso.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=peso.profile_id and bls.object_type=3 and
            peso.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where peso.profile_id = #{profileId}
        AND `peso`.ktype_id = 0
        AND `peso`.order_deliver_required = 1
        AND `peso`.local_trade_state != 5
        AND `peso`.deleted = 0
        AND date_sub(CURDATE(), INTERVAL 29 DAY) &lt;= `peso`.trade_create_time
        AND (`peso`.order_sale_type  not in(3,6) OR (`peso`.order_sale_type=3 AND `peso`.platform_parent_order_id=''))
    </select>

    <select id="getEshopOrderBigDataIds" resultType="java.math.BigInteger">
        select mark_data_id
        from pl_eshop_order_mark where profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            AND order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="markCodes!=null and markCodes.size>0">
            AND mark_code IN
            <foreach collection="markCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="markTarget != null and markTarget == 0">
            AND mark_target=0
        </if>
        <if test="markTarget != null and markTarget == 1">
            AND mark_target=1
        </if>
        <if test="createType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MarkCreateType@DownloadSysCalc">
            AND create_type=0
        </if>
        <if test="createType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MarkCreateType@ManualMark">
            AND create_type=1
        </if>
        <if test="createType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MarkCreateType@SystemCalc">
            AND create_type=2
        </if>
        AND mark_data_id !=0
    </select>

    <select id="queryOrdermarkData"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_order_mark
    </select>


    <update id="updateAdcvanceOrderBtype">
        update td_orderbill_core
        set btype_id=#{btypeId},
            balance_btype_id=#{balanceBtypeId}
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </update>


    <select id="querySimpleAdvanceOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity">
        SELECT c.submit_send_state,o.profile_id,c.vchcode,c.btype_id,c.balance_btype_id
        FROM td_orderbill_platform o
        left join td_orderbill_core c on o.vchcode=c.vchcode
        where o.profile_id = #{profileId}
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="tradeOrderId!=null and tradeOrderId!=''">
            and o.trade_id=#{tradeOrderId}
        </if>
        limit 1
    </select>
    <select id="getRefundOtypeIdsNotDaiYunYin" resultType="java.math.BigInteger" parameterType="java.math.BigInteger">
        select id
        from base_otype
        where profile_id = #{profileId}
          and ocategory != 2
    </select>

    <insert id="insertDetailLiveBroadcast">
        insert into pl_eshop_sale_order_detail_live_broadcast(profile_id, eshop_id, eshop_order_id, detail_id,
                                                              platform_anchor_id, platform_anchor_name,
                                                              platform_live_room_id,
                                                              live_brodcast_session_id, create_time, update_time)
        VALUES (#{profileId}, #{eshopId}, #{eshopOrderId}, #{detailId},
                #{platformAnchorId}, #{platformAnchorName}, #{platformLiveRoomId},
                #{liveBrodcastSessionId}, #{createTime}, #{updateTime})
        ON DUPLICATE KEY
            UPDATE `detail_id`=#{detailId},
                   `profile_id`=#{profileId}
    </insert>

    <insert id="batchInsertDetailLiveBroadcast">
        insert into pl_eshop_sale_order_detail_live_broadcast(profile_id, eshop_id, eshop_order_id, detail_id,
                                                              platform_anchor_id, platform_anchor_name,
                                                              platform_live_room_id,
                                                              live_brodcast_session_id, create_time, update_time)
        VALUES
        <foreach collection="list" item="liveBroadcast" separator=",">
            (#{liveBroadcast.profileId}, #{liveBroadcast.eshopId}, #{liveBroadcast.eshopOrderId}, #{liveBroadcast.detailId},
                #{liveBroadcast.platformAnchorId}, #{liveBroadcast.platformAnchorName}, #{liveBroadcast.platformLiveRoomId},
                #{liveBroadcast.liveBrodcastSessionId}, #{liveBroadcast.createTime}, #{liveBroadcast.updateTime})
        </foreach>
        ON DUPLICATE KEY
            UPDATE `detail_id`=VALUES(detail_id),
                   `profile_id`=VALUES(profile_id)
    </insert>


    <insert id="insertDetailTiming">
        insert into pl_eshop_sale_order_detail_timing(id, eshop_order_id, eshop_order_detail_id, profile_id,
                                                      promised_send_time,
                                                      create_time, update_time)
        VALUES (#{id}, #{eshopOrderId}, #{eshopOrderDetailId}, #{profileId}, #{promisedSendTime},
                #{createTime}, #{updateTime})
        ON DUPLICATE KEY
            UPDATE `id`=#{id},
                   `profile_id`=#{profileId}
    </insert>

    <insert id="batchInsertDetailTiming">
        insert into pl_eshop_sale_order_detail_timing(id, eshop_order_id, eshop_order_detail_id, profile_id,
                                                      promised_send_time,
                                                      create_time, update_time)
        VALUES
        <foreach collection="list" item="timing" separator=",">
            (#{timing.id}, #{timing.eshopOrderId}, #{timing.eshopOrderDetailId}, #{timing.profileId}, #{timing.promisedSendTime},
                #{timing.createTime}, #{timing.updateTime})
        </foreach>
        ON DUPLICATE KEY
            UPDATE `id`=VALUES(id),
                   `profile_id`=VALUES(profile_id)
    </insert>

    <insert id="insertDetailBatch">
        insert into pl_eshop_sale_order_detail_batch(id, eshop_order_id, eshop_order_detail_id, profile_id, batchno,
                                                     produce_date, expire_date, qty, sub_qty, unit_qty, batch_price,
                                                     create_time, update_time)
        VALUES (#{id}, #{eshopOrderId}, #{eshopOrderDetailId}, #{profileId}, #{batchno},
                #{produceDate}, #{expireDate}, #{qty}, #{subQty}, #{unitQty}, #{batchPrice},
                #{createTime}, #{updateTime})
        ON DUPLICATE KEY
            UPDATE `id`=#{id},
                   `profile_id`=#{profileId}
    </insert>

    <insert id="batchInsertDetailBatch">
        insert into pl_eshop_sale_order_detail_batch(id, eshop_order_id, eshop_order_detail_id, profile_id, batchno,
                                                     produce_date, expire_date, qty, sub_qty, unit_qty, batch_price,
                                                     create_time, update_time)
        VALUES
        <foreach collection="list" item="batch" separator=",">
            (#{batch.id}, #{batch.eshopOrderId}, #{batch.eshopOrderDetailId}, #{batch.profileId}, #{batch.batchno},
                #{batch.produceDate}, #{batch.expireDate}, #{batch.qty}, #{batch.subQty}, #{batch.unitQty}, #{batch.batchPrice},
                #{batch.createTime}, #{batch.updateTime})
        </foreach>
        ON DUPLICATE KEY
            UPDATE `id`=VALUES(id),
                   `profile_id`=VALUES(profile_id)
    </insert>

    <insert id="batchInsertDetailSerialnos">
        insert into pl_eshop_sale_order_detail_serialno(id, eshop_order_id, eshop_order_detail_id, profile_id, batchno,
        produce_date, expire_date, qty, sub_qty, unit_qty, batch_price,
        inout_type, snno, sn1, sn2, sn3, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.eshopOrderId}, #{item.eshopOrderDetailId}, #{item.profileId}, #{item.batchno},
            #{item.produceDate},#{item.expireDate},#{item.qty},#{item.subQty},#{item.unitQty},#{item.batchPrice},
            #{item.inoutType},#{item.snno},#{item.sn1},#{item.sn2},#{item.sn3},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>

    <insert id="batchInsertDetailGiftRelations">
        insert into pl_eshop_saleorder_detail_gift_relation(id, profile_id, eshop_order_id, eshop_order_detail_id,
        source_eshop_order_id,
        source_eshop_order_detail_id, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.profileId}, #{item.eshopOrderId}, #{item.eshopOrderDetailId},
            #{item.sourceEshopOrderId},
            #{item.sourceEshopOrderDetailId},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>

    <insert id="batchInsertDetailFreightList">
        INSERT IGNORE INTO pl_eshop_order_detail_freight
            (id, trade_order_id, profile_id, otype_id, trade_order_detail_id,
             freight_name, freight_bill_no, extend_info, create_type, send_qty,
             sync_state, sync_type, has_last, hash_key, freight_code,
             create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.tradeOrderId}, #{item.profileId}, #{item.otypeId}, #{item.tradeOrderDetailId},
            #{item.freightName}, #{item.freightBillNo}, #{item.extendInfo}, #{item.createType}, #{item.sendQty},
            #{item.syncState}, #{item.syncType}, #{item.hasLast}, #{item.hashKey}, #{item.freightCode},
            #{item.createTime},#{item.updateTime})
        </foreach>
    </insert>

    <insert id="insertDetailCombolLiveBroadcast">
        insert into pl_eshop_sale_order_detail_combo_live_broadcast(profile_id, eshop_id, eshop_order_id,
                                                                    eshop_order_detail_combo_row_id, platform_anchor_id,
                                                                    platform_anchor_name, platform_live_room_id,
                                                                    live_brodcast_session_id, create_time, update_time)
        VALUES (#{profileId}, #{eshopId}, #{eshopOrderId}, #{eshopOrderDetailComboRowId},
                #{platformAnchorId}, #{platformAnchorName}, #{platformLiveRoomId},
                #{liveBrodcastSessionId}, #{createTime}, #{updateTime})
        ON DUPLICATE KEY
            UPDATE `eshop_order_detail_combo_row_id`=#{eshopOrderDetailComboRowId},
                   `profile_id`=#{profileId}
    </insert>

    <insert id="batchInsertDetailCombolLiveBroadcast">
        insert into pl_eshop_sale_order_detail_combo_live_broadcast(profile_id, eshop_id, eshop_order_id,
                                                                    eshop_order_detail_combo_row_id, platform_anchor_id,
                                                                    platform_anchor_name, platform_live_room_id,
                                                                    live_brodcast_session_id, create_time, update_time)
        VALUES
        <foreach collection="list" item="liveBroadcast" separator=",">
            (#{liveBroadcast.profileId}, #{liveBroadcast.eshopId}, #{liveBroadcast.eshopOrderId}, #{liveBroadcast.eshopOrderDetailComboRowId},
                #{liveBroadcast.platformAnchorId}, #{liveBroadcast.platformAnchorName}, #{liveBroadcast.platformLiveRoomId},
                #{liveBroadcast.liveBrodcastSessionId}, #{liveBroadcast.createTime}, #{liveBroadcast.updateTime})
        </foreach>
        ON DUPLICATE KEY
            UPDATE `eshop_order_detail_combo_row_id`=VALUES(eshop_order_detail_combo_row_id),
                   `profile_id`=VALUES(profile_id)
    </insert>


    <delete id="deleteDetailLiveBroadcast">
        delete
        from pl_eshop_sale_order_detail_live_broadcast
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="deleteDetailComboLiveBroadcast">
        delete
        from pl_eshop_sale_order_detail_combo_live_broadcast
        where profile_id = #{profileId}
        and eshop_order_id = #{eshopOrderId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_combo_row_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="thoroughPhysicalDeleteSaleOrder">
        delete
        from pl_eshop_sale_order_extend
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_invoice
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_timing
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_distribution_buyer
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail_batch
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail_purchase
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail_extend
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail_live_broadcast
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail_serialno
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail_timing
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail_distribution_buyer
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail_combo
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail_combo_live_broadcast
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail_combo_purchase
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order_detail_combo_distribution_buyer
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_submit_batch
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_submit_batch_detail
        where profile_id = #{profileId}
          and eshop_order_id = (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId}
                                  and trade_order_id = #{tradeOrderId}
                                  and otype_id = #{otypeId}
                                  and deleted = 1);
        delete
        from pl_eshop_sale_order
        where profile_id = #{profileId}
          and trade_order_id = #{tradeOrderId}
          and otype_id = #{otypeId}
          and deleted = 1;
    </delete>
    <delete id="deleteEshopOrderMarkAndDeleteMarkData">
        delete m,md
        from pl_eshop_order_mark m
        JOIN mark_data md on  m.mark_data_id = md.id
        where m.profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            AND m.order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND m.detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="markCodes!=null and markCodes.size>0">
            AND m.mark_code IN
            <foreach collection="markCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="noDeleteMarkCodes!=null and noDeleteMarkCodes.size>0">
            AND m.mark_code NOT IN
            <foreach collection="noDeleteMarkCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="markTarget != null and markTarget == 0">
            AND m.mark_target=0
        </if>
        <if test="markTarget != null and markTarget == 1">
            AND m.mark_target=1
        </if>
        <if test="createType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MarkCreateType@DownloadSysCalc">
            AND m.create_type=0
        </if>
        <if test="createType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MarkCreateType@ManualMark">
            AND m.create_type=1
        </if>
        <if test="createType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MarkCreateType@SystemCalc">
            AND m.create_type=2
        </if>
    </delete>

    <delete id="batchThoroughPhysicalDeleteSaleOrder">
        delete
        from pl_eshop_sale_order_extend
        where profile_id = #{profileId}
          and eshop_order_id in (select id
                                from pl_eshop_sale_order
                                where profile_id = #{profileId} and deleted = 1
                                        (trade_order_id, otype_id) IN
                                        <foreach collection="list" item="order" open="(" separator="," close=")">
                                            (#{order.tradeOrderId}, #{order.otypeId})
                                        </foreach>
                                            );

        delete
        from pl_eshop_sale_order_invoice
        where profile_id = #{profileId}
        and eshop_order_id in (select id
                from pl_eshop_sale_order
                where profile_id = #{profileId} and deleted = 1
                (trade_order_id, otype_id) IN
                <foreach collection="list" item="order" open="(" separator="," close=")">
                    (#{order.tradeOrderId}, #{order.otypeId})
                </foreach>
                );

        delete
        from pl_eshop_sale_order_timing
        where profile_id = #{profileId}
        and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_distribution_buyer
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail_batch
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail_purchase
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail_extend
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail_live_broadcast
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail_serialno
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail_timing
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail_distribution_buyer
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail_combo
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail_combo_live_broadcast
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail_combo_purchase
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order_detail_combo_distribution_buyer
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_submit_batch
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_submit_batch_detail
        where profile_id = #{profileId}
            and eshop_order_id in (select id
            from pl_eshop_sale_order
            where profile_id = #{profileId} and deleted = 1
            (trade_order_id, otype_id) IN
            <foreach collection="list" item="order" open="(" separator="," close=")">
                (#{order.tradeOrderId}, #{order.otypeId})
            </foreach>
            );

        delete
        from pl_eshop_sale_order
        where profile_id = #{profileId} and
        (trade_order_id, otype_id) IN
        <foreach collection="list" item="order" open="(" separator="," close=")">
            (#{order.tradeOrderId}, #{order.otypeId})
        </foreach>
          and deleted = 1;
    </delete>

    <select id="getExistTradeIds" resultType="java.lang.String">
        select trade_order_id from pl_eshop_sale_order where profile_id=#{profileId}
        AND otype_id IN
        <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
            #{otypeId}
        </foreach>
        AND trade_order_id IN
        <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
            #{tid}
        </foreach>
    </select>

    <select id="querySaleOrderSubmitDestination"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        select o.process_state, e.process_type
        from pl_eshop_sale_order o
                 left join pl_eshop_config e on o.profile_id = e.profile_id and o.otype_id = e.eshop_id
        where o.profile_id = #{profileId}
          and o.otype_id = #{eshopId}
          and o.id = #{id}
    </select>

    <select id="querySaleOrderFullInfoList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        select od.*,e.fullname as otypeName,bo.ocategory,
        e.eshop_type as shopType,abb.pr_total as btypePrTotal,ec.btype_generate_type,
        be.fullname as etypeName,bk.fullname as ktypeName,
        bb.fullname as btypeName,
        bd.fullname as departmentName,
        `timing`.eshop_order_id AS 'timing.eshop_order_id',
        `timing`.profile_id AS 'timing.profile_id',
        `timing`.cn_service AS 'timing.cn_service',
        `timing`.plan_send_time AS 'timing.plan_send_time',
        `timing`.send_time AS 'timing.send_time',
        `timing`.system_timing AS 'timing.system_timing',
        `timing`.create_time AS 'timing.create_time',
        `timing`.update_time AS 'timing.update_time',
        `timing`.timing_type AS 'timing.timing_type',
        buyer.ai as 'eshopBuyer.ai',
        buyer.customer_shop_account as 'eshopBuyer.customerShopAccount'
        ,od.order_sale_type
        from pl_eshop_sale_order od
        left join pl_eshop_sale_order_timing timing on timing.profile_id=od.profile_id and timing.eshop_order_id=od.id
        left join pl_eshop e on e.profile_id=od.profile_id and e.otype_id=od.otype_id
        left join base_otype bo on bo.profile_id=od.profile_id and bo.id=od.otype_id
        left join base_etype be on be.profile_id=od.profile_id and be.id=od.etype_id
        left join base_ktype bk on bk.profile_id=od.profile_id and bk.id=od.ktype_id
        left join base_btype bb on bb.profile_id=od.profile_id and bb.id=od.btype_id
        left join base_dtype bd on bd.profile_id=od.profile_id and bd.id=od.dtype_id
        left join acc_btype_balance abb on abb.profile_id=od.profile_id and abb.btype_id=od.btype_id
        left join pl_eshop_config ec on ec.profile_id=od.profile_id and ec.eshop_id=od.otype_id
        left join pl_buyer buyer on buyer.buyer_id = od.buyer_id and buyer.profile_id = od.profile_id
        where od.profile_id=#{profileId}
        <if test="tradeOrderId!=null and tradeOrderId!=''">
            and od.trade_order_id=#{tradeOrderId}
        </if>
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and od.id=#{eshopOrderId}
        </if>
        <if test="otypeIds!=null and otypeIds.size()>0">
            AND `od`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeid">
                #{otypeid}
            </foreach>
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND `od`.trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
    </select>

    <select id="queryDetailSkuInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.OrderDetailOnlineSkuInfo">
        select pesod.profile_id,
        pesod.otype_id,
        peso.trade_order_id as tradeId,
        pesod.eshop_order_id as orderId,
        pesod.id as detailId,
        pesod.platform_sku_id,
        pesod.platform_ptype_id as platformNumId,
        pesod.platform_ptype_xcode as platformXcode,
        pesod.platform_properties_name as platformProperties,
        pesod.trade_order_detail_id as oid
        from pl_eshop_sale_order_detail pesod
        left join pl_eshop_sale_order peso on pesod.profile_id=peso.profile_id and pesod.eshop_order_id=peso.id
        where pesod.profile_id=#{profileId}
        and pesod.otype_id=#{eshopId}
        AND peso.trade_order_id IN
        <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
            #{tid}
        </foreach>
    </select>

    <select id="queryOrderState"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        select od.local_refund_state from pl_eshop_sale_order od
        where od.profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and od.id=#{eshopOrderId}
        </if>
        <if test="tradeOrderId!=null and tradeOrderId!=''">
            and od.trade_order_id=#{tradeOrderId}
        </if>
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `od`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
    </select>

    <select id="getInfoByDeliverOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSaleChannelReport">
        select p.fullname,p.usercode,x.xcode,p.ptype_type,p.standard,f.fullbarcode,count(distinct c.vchcode) as
        orderCount,-1 * sum(c.qty) as ptypeCount,pu.unit_name,
        -1 * sum(a.currency_dised_taxed_total) as total,x.sku_id,x.ptype_id,x.unit_id
        from base_ptype_sku s
        left join base_ptype p on p.profile_id = s.profile_id and p.id = s.ptype_id
        left join base_ptype_unit pu on pu.profile_id = s.profile_id and pu.ptype_id =p.id and unit_code=1
        left join base_ptype_xcode x on x.profile_id = s.profile_id and x.ptype_id = p.id and x.sku_id = s.id and
        x.defaulted = 1
        left join base_ptype_fullbarcode f on f.profile_id = s.profile_id and f.ptype_id = s.ptype_id and f.sku_id =
        s.id and f.unit_id = x.unit_id and f.defaulted = 1
        left join td_bill_detail_core c on c.profile_id = s.profile_id and c.ptype_id = x.ptype_id and c.sku_id =
        x.sku_id
        left join td_bill_detail_assinfo a on a.profile_id = s.profile_id and a.unit_id = x.unit_id and a.vchcode =
        c.vchcode and a.detail_id = c.detail_id
        left join td_bill_detail_deliver tbdd on tbdd.profile_id = s.profile_id and tbdd.detail_id = c.detail_id
        left join td_bill_deliver tbd on tbd.profile_id = s.profile_id and tbd.vchcode = tbdd.vchcode
        left join pl_eshop_sale_order peso on peso.profile_id = s.profile_id and peso.id = tbd.order_id
        where s.profile_id = #{profileId}
        <if test="excludeRefund = true">
            and peso.local_refund_state &lt;&gt; 4
        </if>
        <if test="timeType==0">
            and peso.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==1">
            and peso.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==2">
            and tbd.deliver_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==3">
            and peso.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        <if test="eshopId != null and eshopId.size()>0">
            and c.otype_id in
            <foreach collection="eshopId" item="id" close=")" open="(" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="ptypeIds != null and ptypeIds.size()>0">
            and p.id in
            <foreach collection="ptypeIds" item="ptypeId" close=")" open="(" separator=",">
                #{ptypeId}
            </foreach>
        </if>
        <if test="ptypeFullname != null and ptypeFullname.size()>0">
            and p.fullname in
            <foreach collection="ptypeFullname" item="fullname" close=")" open="(" separator=",">
                #{fullname}
            </foreach>
        </if>
        group by p.fullname,p.usercode,x.xcode
    </select>


    <select id="getInfoBySaleOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSaleChannelReport">
        select * from (
        select p.fullname , p.usercode as usercode, x.xcode,p.ptype_type, p.standard,f.fullbarcode as
        barcode,count(distinct peso.trade_order_id) as
        orderCount,sum(pesod.unit_qty) as ptypeCount,pu.unit_name,GROUP_CONCAT(pesod.platform_sku_id) platformPtypeId,
        sum(pesod.dised_taxed_total) as
        total,pesod.sku_id as sku_id,pesod.ptype_id
        ,pesod.unit
        as unit_id
        from pl_eshop_sale_order_detail pesod
        left join base_ptype_sku s on s.profile_id = pesod.profile_id and s.ptype_id = pesod.ptype_id and s.id =
        pesod.sku_id
        left join base_ptype p on p.profile_id = s.profile_id and p.id = s.ptype_id
        left join base_ptype_xcode x on x.profile_id = s.profile_id and x.ptype_id = p.id and x.sku_id = s.id and
        unit_id = pesod.unit and defaulted=1
        left join base_ptype_unit pu on pu.profile_id = s.profile_id and pu.id = pesod.unit
        left join base_ptype_fullbarcode f on f.profile_id = pesod.profile_id and f.ptype_id = pesod.ptype_id and
        f.sku_id =
        pesod.sku_id and f.unit_id = pesod.unit and f.defaulted = 1
        left join pl_eshop_sale_order_extend pesoe on pesoe.profile_id = s.profile_id and pesoe.eshop_order_id =
        pesod.eshop_order_id
        left join pl_eshop_sale_order peso on peso.profile_id = s.profile_id and peso.id = pesod.eshop_order_id and
        peso.deleted=0
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=s.profile_id and object_type=3 and
            pesod.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where s.profile_id = #{profileId} and pesod.combo_row_id=0 and peso.create_type &lt;&gt; 2
        AND (`peso`.order_sale_type  not in(3,6) OR (`peso`.order_sale_type=3 AND `peso`.platform_parent_order_id=''))
        AND peso.local_trade_state &lt;&gt; 5
        <if test="excludeRefund">
            and peso.local_refund_state &lt;&gt; 4 AND pesod.local_refund_state &lt;&gt; 4
        </if>
        <if test="timeType==0">
            and peso.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==1">
            and peso.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==2">
            and pesoe.platform_send_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==3">
            and peso.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        <if test="eshopId != null and eshopId.size()>0">
            and pesod.otype_id in
            <foreach collection="eshopId" item="id" close=")" open="(" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="ptypeIds != null and ptypeIds.size()>0">
            and p.id in
            <foreach collection="ptypeIds" item="ptypeId" close=")" open="(" separator=",">
                #{ptypeId}
            </foreach>
        </if>
        <if test="ptypeFullname != null and ptypeFullname.size()>0">
            and p.fullname in
            <foreach collection="ptypeFullname" item="fullname" close=")" open="(" separator=",">
                #{fullname}
            </foreach>
        </if>
        group by p.fullname,p.usercode,s.id,pu.id
        union
        select tmp.fullname,
        tmp.usercode,
        xcode,
        tmp.ptype_type,
        tmp.standard,
        barcode,
        count(distinct peso.trade_order_id) as orderCount,
        sum(tmp.qty) as ptypeCount,
        '' as unit_name,
        GROUP_CONCAT(tmp.platform_sku_id) platformPtypeId,
        sum(tmp.dised_taxed_total) as total,
        0 as sku_id,
        tmp.ptype_id,
        0 as unit_id
        from (select pesodc.eshop_order_detail_combo_row_id,pesodc.eshop_order_id,pesod.platform_sku_id,
        pesodc.dised_taxed_total,
        pesodc.qty,
        pesodc.profile_id,
        p.fullname,
        p.usercode as usercode,
        '' as xcode,
        p.ptype_type,
        p.standard,
        '' as barcode,
        0 as sku_id,
        p.id as ptype_id,
        0 as unit_id
        from pl_eshop_sale_order_detail pesod
        left join pl_eshop_sale_order_detail_combo pesodc
        on pesodc.profile_id = pesod.profile_id and pesodc.eshop_order_id = pesod.eshop_order_id
        left join pl_eshop_sale_order peso
        on peso.profile_id = pesodc.profile_id and peso.id = pesodc.eshop_order_id and peso.deleted=0
        left join base_ptype_fullbarcode f
        on f.profile_id = pesodc.profile_id and f.ptype_id = pesodc.combo_id and f.defaulted = 1
        left join base_ptype p on p.profile_id = pesodc.profile_id and p.id = pesodc.combo_id
        left join pl_eshop_sale_order_extend pesoe
        on pesoe.profile_id = pesodc.profile_id and pesoe.eshop_order_id = pesodc.eshop_order_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=pesodc.profile_id and object_type=3 and
            peso.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where pesodc.profile_id = #{profileId} and peso.create_type &lt;&gt; 2 and peso.local_trade_state &lt;&gt; 5 AND
        pesod.local_refund_state &lt;&gt; 4
        AND (`peso`.order_sale_type  not in(3,6) OR (`peso`.order_sale_type=3 AND `peso`.platform_parent_order_id=''))
        <if test="excludeRefund">
            and peso.local_refund_state &lt;&gt; 4
        </if>
        <if test="timeType==0">
            and peso.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==1">
            and peso.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==2">
            and pesoe.platform_send_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==3">
            and peso.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        <if test="eshopId != null and eshopId.size()>0">
            and peso.otype_id in
            <foreach collection="eshopId" item="id" close=")" open="(" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="ptypeIds != null and ptypeIds.size()>0">
            and p.id in
            <foreach collection="ptypeIds" item="ptypeId" close=")" open="(" separator=",">
                #{ptypeId}
            </foreach>
        </if>
        <if test="ptypeFullname != null and ptypeFullname.size()>0">
            and p.fullname in
            <foreach collection="ptypeFullname" item="fullname" close=")" open="(" separator=",">
                #{fullname}
            </foreach>
        </if>
        group by pesodc.eshop_order_detail_combo_row_id
        ) tmp
        left join pl_eshop_sale_order peso
        on peso.profile_id = tmp.profile_id and peso.id = tmp.`eshop_order_id` and peso.deleted=0
        group by tmp.fullname, tmp.usercode
        ) tmp2
        where tmp2.fullname is not null
    </select>


    <select id="getOrderGoodsDetailInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PlatformInfo">
        select count(distinct peso.trade_order_id) as platformOrderCount,
        sum(pesod.unit_qty) as platformSaleqty,
        sum(pesod.dised_taxed_total) as platformTotal
        from pl_eshop_sale_order_detail pesod
        left join pl_eshop_sale_order peso
        on peso.profile_id = pesod.profile_id and peso.id = pesod.eshop_order_id and peso.deleted=0
        left join pl_eshop_sale_order_extend pesoe on pesoe.profile_id = pesod.profile_id and pesoe.eshop_order_id =
        pesod.eshop_order_id
        where pesod.profile_id = #{profileId}
        and pesod.ptype_id = #{ptypeId}
        and pesod.sku_id = #{skuId}
        and pesod.unit = #{unitId}
        and pesod.platform_properties_name = #{platformPropertiesName}
        and pesod.platform_ptype_id = #{platfromNumId}
        and peso.create_type &lt;&gt; 2 and peso.local_trade_state &lt;&gt; 5
        <if test="platformPropertiesName != null and platformPropertiesName !=''">
            and pesod.platform_properties_name = #{platformPropertiesName}
        </if>
        <if test="excludeRefund">
            and peso.local_refund_state &lt;&gt; 4 AND pesod.local_refund_state &lt;&gt; 4
        </if>
        <if test="timeType==0">
            and peso.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==1">
            and peso.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==2">
            and pesoe.platform_send_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==3">
            and peso.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by pesod.platform_ptype_id,pesod.platform_properties_name
        union
        select count(distinct peso.trade_order_id) as platformOrderCount,
        sum(co.qty) as platformSaleqty,
        sum(co.dised_taxed_total) as platformTotal
        from pl_eshop_sale_order peso
        left join pl_eshop_sale_order_extend pesoe on pesoe.profile_id = peso.profile_id and pesoe.eshop_order_id =
        peso.id
        left join pl_eshop_sale_order_detail_combo co on co.profile_id=peso.profile_id and co.eshop_order_id = peso.id
        where peso.profile_id = #{profileId}
        and co.combo_id = #{ptypeId}
        and peso.create_type &lt;&gt; 2 and peso.local_trade_state &lt;&gt; 5
        <if test="excludeRefund">
            and peso.local_refund_state &lt;&gt; 4
        </if>
        <if test="timeType==0">
            and peso.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==1">
            and peso.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==2">
            and pesoe.platform_send_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==3">
            and peso.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        and co.eshop_order_detail_combo_row_id in
        (select distinct combo_row_id from pl_eshop_sale_order_detail where profile_id=peso.profile_id and
        eshop_order_id = peso.id and platform_ptype_id =#{platfromNumId}
        <if test="excludeRefund">
            AND local_refund_state &lt;&gt; 4
        </if>
        <if test="platformPropertiesName != null and platformPropertiesName !=''">
            and platform_properties_name = #{platformPropertiesName}
        </if>)
        limit 1
    </select>
    <select id="getDeliverOrderInfoByPtypeInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PlatformInfo">
        select e.eshop_type as shopType,
        e.fullname as eshopName,
        e.otype_id as eshopId,
        pm.platform_fullname,
        pm.platform_num_id,
        ps.platform_properties_name,
        ps.platform_sku_id,
        ps.platform_xcode,
        pm.platform_stock_state
        from base_ptype_sku s
        left join base_ptype p on p.profile_id = s.profile_id and p.id = s.ptype_id
        left join pl_eshop e on e.profile_id = s.profile_id
        join pl_eshop_product_sku_mapping psm
        on psm.profile_id = s.profile_id and psm.eshop_id = e.otype_id and
        psm.sku_id = s.id and psm.ptype_id = p.id
        left join pl_eshop_sale_order_detail pesod
        on pesod.profile_id = s.profile_id and pesod.ptype_id = psm.ptype_id and pesod.otype_id = e.otype_id and
        pesod.sku_id = psm.sku_id and pesod.unit = psm.unit_id
        left join pl_eshop_sale_order peso
        on peso.profile_id = s.profile_id and peso.id = pesod.eshop_order_id
        left join pl_eshop_sale_order_extend pesoe on pesoe.profile_id = s.profile_id and pesoe.eshop_order_id =
        pesod.eshop_order_id
        join pl_eshop_product_sku ps on ps.profile_id = s.profile_id and ps.eshop_id = e.otype_id and
        ps.unique_id = psm.unique_id and ps.platform_num_id = pesod.platform_ptype_id
        join pl_eshop_product pm on pm.profile_id = s.profile_id and pm.eshop_id = e.otype_id and
        pm.platform_num_id = ps.platform_num_id
        join pl_eshop_product_sku_expand pse
        on pse.profile_id = s.profile_id and pse.eshop_id = e.otype_id and pse.unique_id = psm.unique_id
        where s.profile_id = #{profileId} and peso.local_refund_state &lt;&gt; 5 and peso.local_trade_state &lt;&gt; 5
        <if test="excludeRefund">
            and peso.local_refund_state &lt;&gt; 4 AND pesod.local_refund_state &lt;&gt; 4
        </if>
        <if test="timeType==0">
            and peso.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==1">
            and peso.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==2">
            and pesoe.platform_send_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==3">
            and peso.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        and s.id = #{skuId}
        and pse.mapping_type = 0
        and e.otype_id = #{eshopId}
        and s.ptype_id = #{ptypeId}
        and psm.unit_id = #{unitId}
        <if test="skuIds != null and skuIds.size()>0">
            and ps.platform_sku_id in
            <foreach collection="skuIds" separator="," open="(" close=")" item="skuId">
                #{skuId}
            </foreach>
        </if>
        union
        select e.eshop_type as shopType,
        e.fullname as eshopName,
        e.otype_id as eshopId,
        pm.platform_fullname,
        pm.platform_num_id,
        ps.platform_properties_name,
        ps.platform_sku_id,
        ps.platform_xcode,
        pm.platform_stock_state
        from pl_eshop_sale_order_detail pesod
        left join pl_eshop e on e.profile_id = pesod.profile_id and e.otype_id =pesod.otype_id
        left join base_ptype_xcode x on x.profile_id = pesod.profile_id and x.xcode = pesod.platform_ptype_xcode
        left join base_ptype_fullbarcode f
        on f.profile_id = pesod.profile_id and f.ptype_id = pesod.ptype_id and f.sku_id = pesod.id and
        f.unit_id = x.unit_id and f.defaulted = 1
        join pl_eshop_product_sku ps
        on ps.profile_id = pesod.profile_id and ps.eshop_id = e.otype_id and ps.platform_xcode = x.xcode and
        ps.platform_num_id = pesod.platform_ptype_id
        join pl_eshop_product pm
        on pm.profile_id = pesod.profile_id and pm.eshop_id = ps.eshop_id and pm.platform_num_id = ps.platform_num_id
        join pl_eshop_product_sku_expand pse
        on pse.profile_id = pesod.profile_id and pse.eshop_id = ps.eshop_id and pse.unique_id = ps.unique_id
        left join pl_eshop_sale_order peso on peso.profile_id = pesod.profile_id and peso.id = pesod.eshop_order_id
        left join pl_eshop_sale_order_extend pesoe
        on pesoe.profile_id = pesod.profile_id and pesoe.eshop_order_id = pesod.eshop_order_id
        where pesod.profile_id = #{profileId} and peso.local_refund_state &lt;&gt; 5 and peso.local_trade_state &lt;&gt;
        5
        and pesod.sku_id = #{skuId}
        and pse.mapping_type = 1
        and ps.eshop_id = #{eshopId}
        and pesod.ptype_id = #{ptypeId}
        and pesod.unit = #{unitId}
        <if test="excludeRefund">
            and peso.local_refund_state &lt;&gt; 4 AND pesod.local_refund_state &lt;&gt; 4
        </if>
        <if test="timeType==0">
            and peso.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==1">
            and peso.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==2">
            and pesoe.platform_send_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==3">
            and peso.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        union
        select e.eshop_type as shopType,
        e.fullname as eshopName,
        e.otype_id as eshopId,
        pm.platform_fullname,
        pm.platform_num_id,
        ps.platform_properties_name,
        ps.platform_sku_id,
        ps.platform_xcode,
        pm.platform_stock_state
        from pl_eshop_sale_order_detail pesod
        left join pl_eshop e on e.profile_id =pesod.profile_id and e.otype_id = pesod.otype_id
        left join pl_eshop_sale_order_detail_combo pesodc on pesodc.profile_id = pesod.profile_id and
        pesodc.eshop_order_detail_combo_row_id=pesod.combo_row_id
        left join pl_eshop_sale_order peso on peso.profile_id = pesod.profile_id and peso.id = pesod.eshop_order_id
        left join pl_eshop_sale_order_extend pesoe on pesoe.profile_id = pesod.profile_id and pesoe.eshop_order_id =
        pesod.eshop_order_id
        left join pl_eshop_product_sku ps on ps.profile_id = pesod.profile_id and ps.eshop_id = pesod.otype_id and
        ps.platform_num_id = pesod.platform_ptype_id and ps.platform_properties_name = pesod.platform_properties_name
        left join pl_eshop_product_sku_mapping psm on psm.profile_id = pesod.profile_id and psm.eshop_id =
        pesod.otype_id and psm.unique_id = ps.unique_id
        left join pl_eshop_product pm on pm.profile_id = pesod.profile_id and pm.eshop_id = psm.eshop_id and
        pm.platform_num_id = ps.platform_num_id
        left join pl_eshop_product_sku_expand pse
        on pse.profile_id = ps.profile_id and pse.eshop_id = ps.eshop_id and pse.unique_id = ps.unique_id
        where pesod.profile_id = #{profileId} and peso.local_refund_state &lt;&gt; 5 and peso.local_trade_state &lt;&gt;
        5
        <if test="excludeRefund">
            and peso.local_refund_state &lt;&gt; 4 AND pesod.local_refund_state &lt;&gt; 4
        </if>
        <if test="timeType==0">
            and peso.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==1">
            and peso.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==2">
            and pesoe.platform_send_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==3">
            and peso.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        and psm.sku_id = #{skuId}
        and pse.mapping_type = 0
        and ps.eshop_id = #{eshopId}
        and psm.ptype_id = #{ptypeId}
        and psm.unit_id = #{unitId}
        <if test="skuIds != null and skuIds.size()>0">
            and ps.platform_sku_id in
            <foreach collection="skuIds" separator="," open="(" close=")" item="skuId">
                #{skuId}
            </foreach>
        </if>
        union
        select e.eshop_type as shopType,
        e.fullname as eshopName,
        e.otype_id as eshopId,
        pm.platform_fullname,
        pm.platform_num_id,
        ps.platform_properties_name,
        ps.platform_sku_id,
        ps.platform_xcode,
        pm.platform_stock_state
        from pl_eshop_sale_order_detail pesod
        left join pl_eshop_sale_order_detail_combo pesodc on pesodc.profile_id = pesod.profile_id and
        pesodc.eshop_order_detail_combo_row_id = pesod.combo_row_id
        left join pl_eshop e on e.profile_id = pesod.profile_id and e.otype_id =pesod.otype_id
        left join base_ptype_xcode x on x.profile_id = pesod.profile_id and x.xcode = pesod.platform_ptype_xcode
        left join base_ptype_fullbarcode f
        on f.profile_id = pesod.profile_id and f.ptype_id = pesod.ptype_id and f.sku_id = pesod.id and
        f.unit_id = x.unit_id and f.defaulted = 1
        join pl_eshop_product_sku ps
        on ps.profile_id = pesod.profile_id and ps.eshop_id = e.otype_id and ps.platform_xcode = x.xcode and
        ps.platform_num_id = pesod.platform_ptype_id
        join pl_eshop_product pm
        on pm.profile_id = pesod.profile_id and pm.eshop_id = ps.eshop_id and pm.platform_num_id = ps.platform_num_id
        join pl_eshop_product_sku_expand pse
        on pse.profile_id = pesod.profile_id and pse.eshop_id = ps.eshop_id and pse.unique_id = ps.unique_id
        left join pl_eshop_sale_order peso on peso.profile_id = pesod.profile_id and peso.id = pesod.eshop_order_id
        left join pl_eshop_sale_order_extend pesoe
        on pesoe.profile_id = pesod.profile_id and pesoe.eshop_order_id = pesod.eshop_order_id
        where pesod.profile_id = #{profileId} and peso.local_refund_state &lt;&gt; 5 and peso.local_trade_state &lt;&gt;
        5
        and pse.mapping_type = 1
        and ps.eshop_id = #{eshopId}
        and pesodc.combo_id = #{ptypeId}
        <if test="excludeRefund">
            and peso.local_refund_state &lt;&gt; 4 AND pesod.local_refund_state &lt;&gt; 4
        </if>
        <if test="timeType==0">
            and peso.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==1">
            and peso.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==2">
            and pesoe.platform_send_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==3">
            and peso.trade_finish_time between #{beginTime} and #{endTime}
        </if>

        union
        select e.eshop_type as shopType,
        e.fullname as eshopName,
        e.otype_id as eshopId,
        pm.platform_fullname,
        pm.platform_num_id,
        ps.platform_properties_name,
        ps.platform_sku_id,
        ps.platform_xcode,
        pm.platform_stock_state
        from pl_eshop_sale_order_detail pesod
        left join pl_eshop_sale_order_detail_combo pesodc on pesodc.profile_id = pesod.profile_id and
        pesodc.eshop_order_detail_combo_row_id = pesod.combo_row_id
        left join pl_eshop e on e.profile_id = pesod.profile_id and e.otype_id =pesod.otype_id
        left join base_ptype x on x.profile_id = pesod.profile_id and x.id = pesodc.combo_id
        left join base_ptype_fullbarcode f
        on f.profile_id = pesod.profile_id and f.ptype_id = pesod.ptype_id and f.sku_id = pesod.id and f.defaulted = 1
        join pl_eshop_product_sku ps
        on ps.profile_id = pesod.profile_id and ps.eshop_id = e.otype_id and ps.platform_xcode = x.usercode and
        ps.platform_num_id = pesod.platform_ptype_id
        join pl_eshop_product pm
        on pm.profile_id = pesod.profile_id and pm.eshop_id = ps.eshop_id and pm.platform_num_id = ps.platform_num_id
        join pl_eshop_product_sku_expand pse
        on pse.profile_id = pesod.profile_id and pse.eshop_id = ps.eshop_id and pse.unique_id = ps.unique_id
        left join pl_eshop_sale_order peso on peso.profile_id = pesod.profile_id and peso.id = pesod.eshop_order_id
        left join pl_eshop_sale_order_extend pesoe
        on pesoe.profile_id = pesod.profile_id and pesoe.eshop_order_id = pesod.eshop_order_id
        where pesod.profile_id = #{profileId} and peso.local_refund_state &lt;&gt; 5 and peso.local_trade_state &lt;&gt;
        5
        and pse.mapping_type = 1
        and ps.eshop_id = #{eshopId}
        and pesodc.combo_id = #{ptypeId}
        <if test="excludeRefund">
            and peso.local_refund_state &lt;&gt; 4 AND pesod.local_refund_state &lt;&gt; 4
        </if>
        <if test="timeType==0">
            and peso.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==1">
            and peso.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==2">
            and pesoe.platform_send_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==3">
            and peso.trade_finish_time between #{beginTime} and #{endTime}
        </if>

    </select>


    <select id="queryEshopByProductMapping"
            resultType="com.wsgjp.ct.sale.biz.jarvis.entity.eshoporder.EshopInfo">
        select e.*
        from pl_eshop_product_sku_mapping m
        left join pl_eshop e on e.otype_id = m.eshop_id and e.profile_id = m.profile_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=m.profile_id and object_type=3 and
            e.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where e.profile_id = #{profileId}
        and m.sku_id = #{skuId}
        and m.ptype_id = #{ptypeId}
        and m.unit_id = #{unitId}
        <if test="eshopIds != null and eshopIds.size()>0">
            and m.eshop_id in
            <foreach collection="eshopIds" item="eshopId" close=")" open="(" separator=",">
                #{eshopId}
            </foreach>
        </if>
        union
        select e.*
        from pl_eshop_product_sku s
        left join pl_eshop e on e.profile_id = s.profile_id and e.otype_id = s.eshop_id
        left join base_ptype_xcode x on x.profile_id = s.profile_id and x.xcode = s.platform_xcode
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=s.profile_id and object_type=3 and
            e.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where s.profile_id = #{profileId}
        and x.sku_id = #{skuId}
        and x.ptype_id = #{ptypeId}
        and x.unit_id = #{unitId}
        <if test="eshopIds != null and eshopIds.size()>0">
            and s.eshop_id in
            <foreach collection="eshopIds" item="eshopId" close=")" open="(" separator=",">
                #{eshopId}
            </foreach>
        </if>
        union
        select e.*
        from pl_eshop_product_sku s
        left join pl_eshop e on e.profile_id = s.profile_id and e.otype_id = s.eshop_id
        left join base_ptype p on p.profile_id = s.profile_id and p.usercode = s.platform_xcode and p.pcategory=2
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=s.profile_id and object_type=3 and
            e.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where s.profile_id = #{profileId}
        and p.id = #{ptypeId}
        <if test="eshopIds != null and eshopIds.size()>0">
            and s.eshop_id in
            <foreach collection="eshopIds" item="eshopId" close=")" open="(" separator=",">
                #{eshopId}
            </foreach>
        </if>
    </select>


    <select id="queryPlatInfoByEshop"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PlatformInfo">
        select e.eshop_type                      as shopType,
               e.fullname                        as eshopName,
               p.platform_fullname,
               s.platform_properties_name,
               s.platform_xcode,
               p.platform_stock_state,
               count(distinct c.vchcode)         as platformOrderCount,
               sum(c.qty)                        as platformSaleqty,
               sum(a.currency_dised_taxed_total) as platformTotal
        from pl_eshop e
                 left join pl_eshop_product_sku_mapping m on m.eshop_id = e.otype_id and m.profile_id = e.profile_id
                 left join pl_eshop_product_sku s
                           on s.eshop_id = e.otype_id and s.profile_id = e.profile_id and s.unique_id = m.unique_id
                 left join pl_eshop_product p on p.eshop_id = e.otype_id and p.profile_id = e.profile_id and
                                                 p.platform_num_id = s.platform_num_id
                 left join pl_eshop_product_sku_expand ex
                           on ex.profile_id = s.profile_id and ex.eshop_id = e.otype_id and ex.unique_id = m.unique_id
                 left join base_ptype_fullbarcode f
                           on f.profile_id = s.profile_id and f.ptype_id = m.ptype_id and f.sku_id = m.sku_id and
                              f.unit_id = m.unit_id and f.defaulted = 1
                 left join td_bill_detail_core c
                           on c.profile_id = s.profile_id and c.ptype_id = m.ptype_id and c.sku_id = m.sku_id and
                              c.otype_id = e.otype_id
                 left join td_bill_detail_assinfo a
                           on a.profile_id = s.profile_id and a.unit_id = m.unit_id and a.vchcode = c.vchcode and
                              a.detail_id = c.detail_id
        where e.profile_id = #{profileId}
          and e.otype_Id = #{eshopId}
          and m.ptype_id = #{ptypeId}
          and m.sku_id = #{skuId}
          and m.unit_id = #{unitId}
          and ex.mapping_type = 0
        group by e.otype_id, s.platform_properties_name
        union
        select e.eshop_type                      as shopType,
               e.fullname                        as eshopName,
               p.platform_fullname,
               s.platform_properties_name,
               s.platform_xcode,
               p.platform_stock_state,
               count(distinct c.vchcode)         as platformOrderCount,
               sum(c.qty)                        as platformSaleqty,
               sum(a.currency_dised_taxed_total) as platformTotal
        from pl_eshop e
                 left join pl_eshop_product_sku s on s.eshop_id = e.otype_id and s.profile_id = e.profile_id
                 left join base_ptype_xcode bpx on bpx.profile_id = e.profile_id and bpx.xcode = s.platform_xcode
                 left join pl_eshop_product p on p.eshop_id = e.otype_id and p.profile_id = e.profile_id and
                                                 p.platform_num_id = s.platform_num_id
                 left join pl_eshop_product_sku_expand ex
                           on ex.profile_id = s.profile_id and ex.eshop_id = e.otype_id and ex.unique_id = s.unique_id
                 left join base_ptype_fullbarcode f
                           on f.profile_id = s.profile_id and f.ptype_id = bpx.ptype_id and f.sku_id = bpx.sku_id and
                              f.unit_id = bpx.unit_id and f.defaulted = 1
                 left join td_bill_detail_core c
                           on c.profile_id = s.profile_id and c.ptype_id = bpx.ptype_id and c.sku_id = bpx.sku_id and
                              c.otype_id = e.otype_id
                 left join td_bill_detail_assinfo a
                           on a.profile_id = s.profile_id and a.unit_id = bpx.unit_id and a.vchcode = c.vchcode and
                              a.detail_id = c.detail_id
        where e.profile_id = #{profileId}
          and e.otype_Id = #{eshopId}
          and s.platform_xcode != ''
          and bpx.ptype_id = #{ptypeId}
          and bpx.sku_id = #{skuId}
          and bpx.unit_id = #{unitId}
          and ex.mapping_type = 1
        group by e.otype_id, s.platform_properties_name
        limit 1
    </select>

    <select id="checkPtypeBelongThatEshop" resultType="java.lang.Boolean">
        select sum(tmp.cou) > 0
        from (select count(*) as cou
              from pl_eshop_product_sku_mapping m
                       left join pl_eshop_product_sku_expand ex
                                 on ex.profile_id = m.profile_id and ex.eshop_id = m.eshop_id and
                                    ex.unique_id = m.unique_id
              where m.profile_id = #{profileId}
                and m.eshop_id = #{eshopId}
                and m.ptype_id = #{ptypeId}
                and m.sku_id = #{skuId}
                and m.unit_id = #{unitId}
                and ex.mapping_type = 0
              union
              select count(*) as cou
              from base_ptype_xcode bpx
                       left join pl_eshop_product_sku s
                                 on s.profile_id = bpx.profile_id and s.eshop_id = #{eshopId} and
                                    s.platform_xcode = bpx.xcode
                       left join pl_eshop_product_sku_expand ex
                                 on ex.profile_id = s.profile_id and ex.eshop_id = s.eshop_id and
                                    ex.unique_id = s.unique_id
              where bpx.profile_id = #{profileId}
                and s.eshop_id = #{eshopId}
                and bpx.ptype_id = #{ptypeId}
                and bpx.sku_id = #{skuId}
                and bpx.unit_id = #{unitId}
                and ex.mapping_type = 1
              union
              select count(*) as cou
              from base_ptype bp
                       left join pl_eshop_product_sku s
                                 on s.profile_id = bp.profile_id and
                                    s.platform_xcode = bp.usercode and bp.pcategory = 2
                       left join pl_eshop_product_sku_expand ex
                                 on ex.profile_id = s.profile_id and ex.eshop_id = s.eshop_id and
                                    ex.unique_id = s.unique_id
              where bp.profile_id = #{profileId}
                and s.eshop_id = #{eshopId}
                and bp.id = #{ptypeId}
                and ex.mapping_type = 1) tmp
    </select>
    <select id="getOrderIdByRefundId" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select relation.target_vchcode
        from td_bill_relation relation
                 left join pl_eshop_sale_order o
                           on relation.profile_id = o.profile_id and relation.target_vchcode = o.id
        where relation.profile_id = #{profileId}
          and relation.source_vchcode = #{id}
          and relation.source_vchtype = 9801
          and relation.target_vchtype = 9802
          and o.local_trade_state != 5
    </select>

    <select id="getSaleOrderTradeIds" resultType="java.lang.String">
        SELECT `order`.trade_order_id from
        pl_eshop_sale_order `order`
        WHERE `order`.profile_id=#{profileId}
        <if test="updateTime!=null">
            AND `order`.update_time <![CDATA[<]]> #{updateTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@TRADE_CREATE_TIME">
            AND `order`.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@TRADE_MODIFIED_TIME">
            AND `order`.trade_modified_time between #{beginTime} and #{endTime}
        </if>
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `order`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="tradeStatus !=null and tradeStatus.size() > 0 and (deliverSendState ==null or deliverSendState!=@com.wsgjp.ct.sale.biz.eshoporder.api.enums.ProcessStatusEnum@SENDED) ">
            and `order`.local_trade_state in
            <foreach collection="tradeStatus" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
        </if>

        <if test="refundStates !=null and refundStates.size() > 0 ">
            and `order`.local_refund_state in
            <foreach collection="refundStates" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
        </if>
        <if test="createType !=null and createType.size() > 0 ">
            and `order`.create_type in
            <foreach collection="createType" close=")" open="(" separator="," item="createTypes">
                #{createTypes}
            </foreach>
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@Submit">
            AND `order`.process_state=1
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@NoSubmit">
            AND `order`.process_state=0
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND `order`.mapping_state=1
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND `order`.mapping_state=0
        </if>
        <if test="null != correctOrderTotal and correctOrderTotal==true">
            AND EXISTS
            (SELECT 1
            FROM (SELECT eshop_order_id,
            SUM(dised_taxed_total) AS detailSum
            FROM pl_eshop_sale_order_detail
            WHERE profile_id = #{profileId}
            GROUP BY eshop_order_id) a
            WHERE `order`.id = a.eshop_order_id
            AND `order`.dised_taxed_total != a.detailSum)
        </if>
        <if test="platformPtypeId !=null and platformPtypeId != '' and platformPropertiesName !=null and platformPropertiesName != ''">
            AND EXISTS(SELECT 1 FROM `pl_eshop_sale_order_detail` AS esd
            WHERE esd.eshop_order_id = order.id and esd.profile_id=#{profileId} AND esd.platform_ptype_id
            =#{platformPtypeId} AND esd.platform_properties_name=#{platformPropertiesName})
            GROUP BY order.id
        </if>
    </select>
    <select id="querySaleOrderListForSelect"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryOrderParameter"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        SELECT
        `order`.trade_order_id,
        `otype`.fullname AS 'otype_name',
        `order`.id,
        `order`.otype_id,
        `order`.profile_id,
        `order`.create_type,
        `order`.local_trade_state,
        `order`.trade_create_time,
        `order`.trade_modified_time,
        `order`.create_time,
        `order`.modified_time,
        `order`.mapping_state,
        `order`.buyer_id,
        `order`.process_state,
        `order`.platform_distributor_name,
        `buyer`.customer_receiver AS 'eshopBuyer.customer_receiver',
        buyer.customer_receiver_country AS 'eshopBuyer.customer_receiver_country',
        customer_receiver_province AS 'eshopBuyer.customer_receiver_province',
        customer_receiver_city AS 'eshopBuyer.customer_receiver_city',
        customer_receiver_district AS 'eshopBuyer.customer_receiver_district',
        customer_receiver_address AS 'eshopBuyer.customer_receiver_address',
        customer_receiver_full_address AS 'eshopBuyer.customer_receiver_full_address',
        customer_shop_account AS 'eshopBuyer.customer_shop_account',
        customer_receiver_town AS 'eshopBuyer.customer_receiver_town',
        di AS 'eshopBuyer.di',
        ai AS 'eshopBuyer.ai',
        ri AS 'eshopBuyer.ri',
        mi AS 'eshopBuyer.mi',
        addri AS 'eshopBuyer.addri',
        `buyer`.profile_id AS 'eshopBuyer.profileId',
        `buyer`.otype_id AS 'eshopBuyer.otypeId',
        `btype`.fullname AS 'btype_name',
        `order`.local_refund_state
        FROM pl_eshop_sale_order `order`
        LEFT JOIN base_otype `otype` ON `otype`.profile_id = `order`.profile_id AND `otype`.id = `order`.otype_id
        LEFT JOIN pl_buyer buyer ON `buyer`.buyer_id = `order`.buyer_id AND `buyer`.profile_id = `order`.profile_id
        LEFT JOIN base_btype btype ON btype.id = `order`.btype_id AND btype.profile_id = `order`.profile_id
        WHERE `order`.profile_id=#{profileId}
        AND (`order`.order_sale_type != 3 OR (`order`.order_sale_type=3 AND `order`.platform_parent_order_id=''))
        <if test="allowOtypeIds!=null and allowOtypeIds.size()>0 and otypeLimited">
            and `order`.otype_id in
            <foreach collection="allowOtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowKtypeIds!=null and allowKtypeIds.size()>0 and ktypeLimited">
            and `order`.Ktype_id in
            <foreach collection="allowKtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowEtypeIds!=null and allowEtypeIds.size()>0 and etypeLimited">
            and `order`.etype_id in
            <foreach collection="allowEtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@TRADE_CREATE_TIME">
            AND `order`.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@PAY_TIME">
            AND `order`.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@DOWNLOAD_TIME">
            AND `order`.create_time between #{beginTime} and #{endTime}
        </if>
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `order`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="tradeStatus !=null and tradeStatus.size() > 0 and (deliverSendState ==null or deliverSendState!=@com.wsgjp.ct.sale.biz.eshoporder.api.enums.ProcessStatusEnum@SENDED) ">
            and `order`.local_trade_state in
            <foreach collection="tradeStatus" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
        </if>
        <if test="tradeStatus !=null and tradeStatus.size() > 0 and deliverSendState !=null and  deliverSendState==@com.wsgjp.ct.sale.biz.eshoporder.api.enums.ProcessStatusEnum@SENDED">
            AND (`order`.local_trade_state in
            <foreach collection="tradeStatus" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
            OR (`order`.local_trade_state in (3,4,5,6)
            ))
        </if>

        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND `order`.mapping_state=1
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND `order`.mapping_state=0
        </if>
        <if test="deleted != null ">
            AND `order`.deleted= #{deleted}
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@Trade_ID and keyArrays!=null and keyArrays.size()>0">
            and trade_order_id in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="btypeId!=null and btypeId>0">
            AND `order`.btype_id=#{btypeId}
        </if>
    </select>
    <select id="querySaleOrderForSelectCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM pl_eshop_sale_order `order`
        WHERE `order`.profile_id=#{profileId}
        AND (`order`.order_sale_type != 3 OR (`order`.order_sale_type=3 AND `order`.platform_parent_order_id=''))
        <if test="allowOtypeIds!=null and allowOtypeIds.size()>0 and otypeLimited">
            and `order`.otype_id in
            <foreach collection="allowOtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowKtypeIds!=null and allowKtypeIds.size()>0 and ktypeLimited">
            and `order`.Ktype_id in
            <foreach collection="allowKtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="allowEtypeIds!=null and allowEtypeIds.size()>0 and etypeLimited">
            and `order`.etype_id in
            <foreach collection="allowEtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@TRADE_CREATE_TIME">
            AND `order`.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@PAY_TIME">
            AND `order`.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@DOWNLOAD_TIME">
            AND `order`.create_time between #{beginTime} and #{endTime}
        </if>
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `order`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="tradeStatus !=null and tradeStatus.size() > 0 and (deliverSendState ==null or deliverSendState!=@com.wsgjp.ct.sale.biz.eshoporder.api.enums.ProcessStatusEnum@SENDED) ">
            and `order`.local_trade_state in
            <foreach collection="tradeStatus" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
        </if>
        <if test="tradeStatus !=null and tradeStatus.size() > 0 and deliverSendState !=null and  deliverSendState==@com.wsgjp.ct.sale.biz.eshoporder.api.enums.ProcessStatusEnum@SENDED">
            AND (`order`.local_trade_state in
            <foreach collection="tradeStatus" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
            OR (`order`.local_trade_state in (3,4,5,6)
            ))
        </if>

        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND `order`.mapping_state=1
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND `order`.mapping_state=0
        </if>
        <if test="deleted != null ">
            AND `order`.deleted= #{deleted}
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@Trade_ID and keyArrays!=null and keyArrays.size()>0">
            and trade_order_id in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="btypeId!=null and btypeId>0">
            AND `order`.btype_id=#{btypeId}
        </if>
    </select>

    <select id="getAreaSaleReport" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopAreaSaleReport">
        select r.customer_receiver_province,
        r.customer_receiver_city,
        r.profile_id,
        r.id,
        r.buyer_id,
        r.eshopName,
        r.eshopId,
        sum(r.orderCount) as orderCount,
        sum(r.ptypeCount) as ptypeCount,
        sum(r.total) as total
        from (select pb.customer_receiver_province,
        pb.customer_receiver_city,ord.profile_id,ord.id,ord.eshopId,ord.buyer_id,ord.eshopName,ord.trade_order_id,
        sum(ord.ptypeCount) as ptypeCount,sum(ord.total) as total,count(ord.id) as orderCount
        from pl_buyer pb
        inner join (select info.profile_id,
        info.id,
        info.eshopId,
        info.buyer_id,
        info.eshopName,
        info.trade_order_id,
        sum(info.ptypeCount) as ptypeCount,
        sum(info.total) as total
        from( select o.profile_id,
        o.id,
        o.otype_id as eshopId,
        o.buyer_id,
        e.fullname as eshopName,
        o.trade_order_id,
        sum(od.unit_qty) as ptypeCount,
        sum(od.dised_taxed_total) as total
        from pl_eshop_sale_order o
        left join pl_eshop_sale_order_detail od
        on od.profile_id = o.profile_id and od.eshop_order_id = o.id
        left join base_otype ot on ot.id = o.otype_id and ot.profile_id = o.profile_id
        left join pl_eshop e on e.profile_id = o.profile_id and e.otype_id = ot.id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=o.profile_id and bls.object_type=3 and
            o.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        <if test="etypeLimited">
            inner join base_limit_scope blse on blse.profile_id=o.profile_id and blse.object_type=1 and
            blse.object_id = o.etype_id
        </if>
        where o.profile_id = #{profileId} and o.local_trade_state in(0,2,3,4,5,6,7) and o.deleted=0 and
        od.combo_row_id=0 and ot.ocategory in(0,1,2)
        AND (`o`.order_sale_type  not in(3,6) OR (`o`.order_sale_type=3 AND `o`.platform_parent_order_id=''))
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by o.id
        union
        select o.profile_id,
        o.id,
        o.otype_id as eshopId,
        o.buyer_id,
        e.fullname as eshopName,
        o.trade_order_id,
        sum(cod.qty) as ptypeCount,
        sum(cod.dised_taxed_total) as total
        from pl_eshop_sale_order o
        inner join pl_eshop_sale_order_detail_combo cod
        on cod.profile_id = o.profile_id and cod.eshop_order_id = o.id
        left join base_otype ot on ot.id = o.otype_id and ot.profile_id = o.profile_id
        left join pl_eshop e on e.profile_id = o.profile_id and e.otype_id = ot.id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=o.profile_id and bls.object_type=3 and
            o.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        <if test="etypeLimited">
            inner join base_limit_scope blse on blse.profile_id=o.profile_id and blse.object_type=1 and
            blse.object_id = o.etype_id
        </if>
        where o.profile_id = #{profileId} and o.local_trade_state in(0,2,3,4,5,6,7) and o.deleted=0
        AND (`o`.order_sale_type  not in(3,6) OR (`o`.order_sale_type=3 AND `o`.platform_parent_order_id=''))
        and ot.ocategory in(0,1,2)
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by o.id) info group by info.id) ord on ord.profile_id = pb.profile_id and ord.buyer_id = pb.buyer_id
        where 1 = 1 and pb.customer_receiver_province is not null and pb.customer_receiver_province != ''
        group by pb.buyer_id) r
        where 1 = 1 and r.customer_receiver_province is not null and r.customer_receiver_province != ''
        <if test="address != null and address != '' and reportType==0">
            and r.customer_receiver_province like CONCAT('%',#{address},'%')
        </if>
        <if test="address != null and address != '' and reportType==1">
            and r.customer_receiver_city like CONCAT('%',#{address},'%')
        </if>
        <if test="reportType != null and reportType==0">
            group by r.customer_receiver_province,r.eshopId
        </if>
        <if test="reportType != null and reportType==1">
            group by r.customer_receiver_city,r.eshopId
        </if>
    </select>


    <select id="getAreaSaleReportCount" resultType="java.lang.Integer">
        select sum(orderCount) from(select r.customer_receiver_province,
        r.customer_receiver_city,
        r.profile_id,
        r.id,
        r.buyer_id,
        r.eshopName,
        r.eshopId,
        sum(r.orderCount) as orderCount,
        sum(r.ptypeCount) as ptypeCount,
        sum(r.total) as total
        from (select pb.customer_receiver_province,
        pb.customer_receiver_city,ord.profile_id,ord.id,ord.eshopId,ord.buyer_id,ord.eshopName,ord.trade_order_id,
        sum(ord.ptypeCount) as ptypeCount,sum(ord.total) as total,count(ord.id) as orderCount
        from pl_buyer pb
        inner join (select info.profile_id,
        info.id,
        info.eshopId,
        info.buyer_id,
        info.eshopName,
        info.trade_order_id,
        sum(info.ptypeCount) as ptypeCount,
        sum(info.total) as total
        from(select o.profile_id,
        o.id,
        o.otype_id as eshopId,
        o.buyer_id,
        e.fullname as eshopName,
        o.trade_order_id,
        sum(od.unit_qty) as ptypeCount,
        sum(od.dised_taxed_total) as total
        from pl_eshop_sale_order o
        left join pl_eshop_sale_order_detail od
        on od.profile_id = o.profile_id and od.eshop_order_id = o.id
        left join pl_eshop e on e.profile_id = o.profile_id and e.otype_id = o.otype_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=o.profile_id and bls.object_type=3 and
            o.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where o.profile_id = #{profileId} and o.local_trade_state in(0,2,3,4,5,6,7) and o.deleted=0 and
        od.combo_row_id=0
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by o.id
        union
        select o.profile_id,
        o.id,
        o.otype_id as eshopId,
        o.buyer_id,
        e.fullname as eshopName,
        o.trade_order_id,
        sum(cod.qty) as ptypeCount,
        sum(cod.dised_taxed_total) as total
        from pl_eshop_sale_order o
        inner join pl_eshop_sale_order_detail_combo cod
        on cod.profile_id = o.profile_id and cod.eshop_order_id = o.id
        left join pl_eshop e on e.profile_id = o.profile_id and e.otype_id = o.otype_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=o.profile_id and bls.object_type=3 and
            o.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where o.profile_id = #{profileId} and o.local_trade_state in(0,2,3,4,5,6,7) and o.deleted=0
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by o.id)info group by info.id) ord on ord.profile_id = pb.profile_id and ord.buyer_id = pb.buyer_id
        where 1 = 1 and pb.customer_receiver_province is not null and pb.customer_receiver_province != ''
        group by pb.buyer_id) r
        where 1 = 1 and r.customer_receiver_province is not null and r.customer_receiver_province != ''
        <if test="address != null and address != '' and reportType==0">
            and r.customer_receiver_province like CONCAT('%',#{address},'%')
        </if>
        <if test="address != null and address != '' and reportType==1">
            and r.customer_receiver_city like CONCAT('%',#{address},'%')
        </if>
        <if test="reportType != null and reportType==0">
            group by r.customer_receiver_province,r.eshopId
        </if>
        <if test="reportType != null and reportType==1">
            group by r.customer_receiver_city,r.eshopId
        </if>)s
    </select>

    <select id="getAreaSaleReporttotalSum" resultType="java.math.BigDecimal">
        select sum(total) from(
        select r.customer_receiver_province,
        r.customer_receiver_city,
        r.profile_id,
        r.id,
        r.buyer_id,
        r.eshopName,
        r.eshopId,
        sum(r.orderCount) as orderCount,
        sum(r.ptypeCount) as ptypeCount,
        sum(r.total) as total
        from (select pb.customer_receiver_province,
        pb.customer_receiver_city,ord.profile_id,ord.id,ord.eshopId,ord.buyer_id,ord.eshopName,ord.trade_order_id,
        sum(ord.ptypeCount) as ptypeCount,sum(ord.total) as total,count(ord.id) as orderCount
        from pl_buyer pb
        inner join (select info.profile_id,
        info.id,
        info.eshopId,
        info.buyer_id,
        info.eshopName,
        info.trade_order_id,
        sum(info.ptypeCount) as ptypeCount,
        sum(info.total) as total
        from(select o.profile_id,
        o.id,
        o.otype_id as eshopId,
        o.buyer_id,
        e.fullname as eshopName,
        o.trade_order_id,
        sum(od.unit_qty) as ptypeCount,
        sum(od.dised_taxed_total) as total
        from pl_eshop_sale_order o
        left join pl_eshop_sale_order_detail od
        on od.profile_id = o.profile_id and od.eshop_order_id = o.id
        left join pl_eshop e on e.profile_id = o.profile_id and e.otype_id = o.otype_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=o.profile_id and bls.object_type=3 and
            o.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where o.profile_id = #{profileId} and o.local_trade_state in(0,2,3,4,5,6,7) and o.deleted=0 and
        od.combo_row_id=0
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by o.id
        union
        select o.profile_id,
        o.id,
        o.otype_id as eshopId,
        o.buyer_id,
        e.fullname as eshopName,
        o.trade_order_id,
        sum(cod.qty) as ptypeCount,
        sum(cod.dised_taxed_total) as total
        from pl_eshop_sale_order o
        inner join pl_eshop_sale_order_detail_combo cod
        on cod.profile_id = o.profile_id and cod.eshop_order_id = o.id
        left join pl_eshop e on e.profile_id = o.profile_id and e.otype_id = o.otype_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=o.profile_id and bls.object_type=3 and
            o.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where o.profile_id = #{profileId} and o.local_trade_state in(0,2,3,4,5,6,7) and o.deleted=0
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by o.id)info group by info.id) ord on ord.profile_id = pb.profile_id and ord.buyer_id = pb.buyer_id
        where 1 = 1 and pb.customer_receiver_province is not null and pb.customer_receiver_province != ''
        group by pb.buyer_id) r
        where 1 = 1 and r.customer_receiver_province is not null and r.customer_receiver_province != ''
        <if test="address != null and address != '' and reportType==0">
            and r.customer_receiver_province like CONCAT('%',#{address},'%')
        </if>
        <if test="address != null and address != '' and reportType==1">
            and r.customer_receiver_city like CONCAT('%',#{address},'%')
        </if>
        <if test="reportType != null and reportType==0">
            group by r.customer_receiver_province,r.eshopId
        </if>
        <if test="reportType != null and reportType==1">
            group by r.customer_receiver_city,r.eshopId
        </if>
        )s
    </select>


    <select id="getRefundReport" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopAreaSaleReport">
        select r.customer_receiver_province,
        r.customer_receiver_city,
        r.profile_id,
        r.id,
        r.buyer_id,
        r.eshopName,
        r.eshopId,
        sum(r.refundOrderCount) as refundOrderCount,
        sum(r.refundPtypeCount) as refundPtypeCount,
        sum(r.refundTotal) as refundTotal
        from (select pb.customer_receiver_province,
        pb.customer_receiver_city,ord.profile_id,ord.id,ord.eshopId,ord.buyer_id,ord.eshopName,ord.trade_refund_order_number,
        sum(ord.refundPtypeCount) as refundPtypeCount,sum(ord.refundTotal) as refundTotal,
        count(ord.id) as refundOrderCount
        from pl_buyer pb
        inner join (select info.id,
        info.profile_id,
        info.eshopId,
        info.buyer_id,
        info.eshopName,
        info.otype_id,
        info.trade_refund_order_number,
        sum(info.refundPtypeCount)as refundPtypeCount,
        sum(info.refundTotal) as refundTotal
        from (select re.profile_id,
        re.id,
        re.otype_id as eshopId,
        re.buyer_id,
        e.fullname as eshopName,
        e.otype_id,
        re.trade_refund_order_number,
        sum(red.apply_refund_qty) as refundPtypeCount,
        sum(red.apply_refund_taxed_total) as refundTotal
        from pl_eshop_refund re
        left join pl_eshop_sale_order o on o.profile_id = re.profile_id and o.trade_order_id = re.trade_order_id
        left join pl_eshop_refund_apply_detail red
        on red.profile_id = re.profile_id and red.refund_order_id = re.id
        left join pl_eshop e on e.profile_id = re.profile_id and e.otype_id = re.otype_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=re.profile_id and bls.object_type=3 and
            re.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where re.profile_id = #{profileId} and re.otype_id=#{eshopId} and re.refund_type in(0,1,5) and re.deleted=0 and
        re.refund_state in(0,1,2,3,5) and red.combo_row_id = 0
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by re.eshop_order_id
        union
        select re.profile_id,
        re.id,
        re.otype_id as eshopId,
        re.buyer_id,
        e.fullname as eshopName,
        e.otype_id,
        re.trade_refund_order_number,
        sum(red.apply_refund_qty) as refundPtypeCount,
        sum(red.apply_refund_taxed_total) as refundTotal
        from pl_eshop_refund re
        left join pl_eshop_sale_order o on o.profile_id = re.profile_id and o.trade_order_id = re.trade_order_id
        left join pl_eshop_refund_apply_detail_combo red
        on red.profile_id = re.profile_id and red.refund_order_id = re.id
        left join pl_eshop e on e.profile_id = re.profile_id and e.otype_id = re.otype_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=re.profile_id and bls.object_type=3 and
            re.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where re.profile_id = #{profileId} and re.otype_id=#{eshopId} and re.refund_type in(0,1,5) and re.deleted=0 and
        re.refund_state
        in(0,1,2,3,5)
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by re.eshop_order_id)info group by info.id)ord on ord.profile_id = pb.profile_id and ord.buyer_id =
        pb.buyer_id
        where 1 = 1
        group by pb.buyer_id) r
        where 1 = 1
        <if test="address != null and address != '' and reportType==0">
            and r.customer_receiver_province like CONCAT('%',#{address},'%')
        </if>
        <if test="address != null and address != '' and reportType==1">
            and r.customer_receiver_city like CONCAT('%',#{address},'%')
        </if>
        <if test="province != null and province != '' and reportType==0">
            and r.customer_receiver_province = #{province}
        </if>
        <if test="city != null and city != '' and reportType==1">
            and r.customer_receiver_city =#{city}
        </if>
        limit 1
    </select>

    <select id="getRefundReportOrderCount" resultType="java.lang.Integer">
        select sum(refundOrderCount) from (
        select r.customer_receiver_province,
        r.customer_receiver_city,
        r.profile_id,
        r.id,
        r.buyer_id,
        r.eshopName,
        r.eshopId,
        sum(r.refundOrderCount) as refundOrderCount,
        sum(r.refundPtypeCount) as refundPtypeCount,
        sum(r.refundTotal) as refundTotal
        from (select pb.customer_receiver_province,
        pb.customer_receiver_city,ord.profile_id,ord.id,ord.eshopId,ord.buyer_id,ord.eshopName,ord.trade_refund_order_number,
        sum(ord.refundPtypeCount) as refundPtypeCount,sum(ord.refundTotal) as refundTotal,count(ord.id) as
        refundOrderCount
        from pl_buyer pb
        inner join (select info.id,
        info.profile_id,
        info.eshopId,
        info.buyer_id,
        info.eshopName,
        info.otype_id,
        info.trade_refund_order_number,
        sum(info.refundPtypeCount)as refundPtypeCount,
        sum(info.refundTotal) as refundTotal
        from (select re.profile_id,
        re.id,
        re.otype_id as eshopId,
        re.buyer_id,
        e.fullname as eshopName,
        e.otype_id,
        re.trade_refund_order_number,
        sum(red.apply_refund_qty) as refundPtypeCount,
        sum(red.apply_refund_taxed_total) as refundTotal
        from pl_eshop_refund re
        left join pl_eshop_sale_order o on o.profile_id = re.profile_id and o.trade_order_id = re.trade_order_id
        left join pl_eshop_refund_apply_detail red
        on red.profile_id = re.profile_id and red.refund_order_id = re.id
        left join pl_eshop e on e.profile_id = re.profile_id and e.otype_id = re.otype_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=re.profile_id and bls.object_type=3 and
            re.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where re.profile_id = #{profileId} and re.refund_type in(0,1,5) and re.deleted=0 and re.refund_state
        in(0,1,2,3,5) and red.combo_row_id = 0
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach
                    collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by re.eshop_order_id
        union
        select re.profile_id,
        re.id,
        re.otype_id as eshopId,
        re.buyer_id,
        e.fullname as eshopName,
        e.otype_id,
        re.trade_refund_order_number,
        sum(red.apply_refund_qty) as refundPtypeCount,
        sum(red.apply_refund_taxed_total) as refundTotal
        from pl_eshop_refund re
        left join pl_eshop_sale_order o on o.profile_id = re.profile_id and o.trade_order_id = re.trade_order_id
        left join pl_eshop_refund_apply_detail_combo red
        on red.profile_id = re.profile_id and red.refund_order_id = re.id
        left join pl_eshop e on e.profile_id = re.profile_id and e.otype_id = re.otype_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=re.profile_id and bls.object_type=3 and
            re.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where re.profile_id = #{profileId} and re.refund_type in(0,1,5) and re.deleted=0 and re.refund_state
        in(0,1,2,3,5)
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach
                    collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by re.eshop_order_id)info group by info.id)ord on ord.profile_id = pb.profile_id and ord.buyer_id =
        pb.buyer_id
        where 1 = 1
        group by pb.buyer_id) r
        where 1 = 1
        <if test="address != null and address != '' and reportType==0">
            and r.customer_receiver_province like CONCAT('%',#{address},'%')
        </if>
        <if test="address != null and address != '' and reportType==1">
            and r.customer_receiver_city like CONCAT('%',#{address},'%')
        </if>
        <if test="reportType != null and reportType==0">
            group by r.customer_receiver_province,r.eshopId
        </if>
        <if test="reportType != null and reportType==1">
            group by r.customer_receiver_city,r.eshopId
        </if>
        ) tmp where tmp.customer_receiver_province is not null and tmp.customer_receiver_province != ''
    </select>

    <select id="getRefundReportOrderTotal" resultType="java.math.BigDecimal">
        select sum(refundTotal) from (
        select r.customer_receiver_province,
        r.customer_receiver_city,
        r.profile_id,
        r.id,
        r.buyer_id,
        r.eshopName,
        r.eshopId,
        count(r.trade_refund_order_number) as refundOrderCount,
        sum(r.refundPtypeCount) as refundPtypeCount,
        sum(r.refundTotal) as refundTotal
        from (select pb.customer_receiver_province,
        pb.customer_receiver_city,ord.profile_id,ord.id,ord.eshopId,ord.buyer_id,ord.eshopName,ord.trade_refund_order_number,
        sum(ord.refundPtypeCount) as refundPtypeCount,sum(ord.refundTotal) as refundTotal
        from pl_buyer pb
        inner join (select info.id,
        info.profile_id,
        info.eshopId,
        info.buyer_id,
        info.eshopName,
        info.otype_id,
        info.trade_refund_order_number,
        sum(info.refundPtypeCount)as refundPtypeCount,
        sum(info.refundTotal) as refundTotal
        from (select re.profile_id,
        re.id,
        re.otype_id as eshopId,
        re.buyer_id,
        e.fullname as eshopName,
        e.otype_id,
        re.trade_refund_order_number,
        sum(red.apply_refund_qty) as refundPtypeCount,
        sum(red.apply_refund_taxed_total) as refundTotal
        from pl_eshop_refund re
        left join pl_eshop_sale_order o on o.profile_id = re.profile_id and o.trade_order_id = re.trade_order_id and
        o.otype_id = re.otype_id
        left join pl_eshop_refund_apply_detail red
        on red.profile_id = re.profile_id and red.refund_order_id = re.id
        left join pl_eshop e on e.profile_id = re.profile_id and e.otype_id = re.otype_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=re.profile_id and bls.object_type=3 and
            re.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where re.profile_id = #{profileId} and re.refund_type in(0,1,5) and re.deleted=0 and re.refund_state
        in(0,1,2,3,5) and red.combo_row_id = 0
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by re.eshop_order_id
        union
        select re.profile_id,
        re.id,
        re.otype_id as eshopId,
        re.buyer_id,
        e.fullname as eshopName,
        e.otype_id,
        re.trade_refund_order_number,
        sum(red.apply_refund_qty) as refundPtypeCount,
        sum(red.apply_refund_taxed_total) as refundTotal
        from pl_eshop_refund re
        left join pl_eshop_sale_order o on o.profile_id = re.profile_id and o.trade_order_id = re.trade_order_id and
        o.otype_id = re.otype_id
        left join pl_eshop_refund_apply_detail_combo red
        on red.profile_id = re.profile_id and red.refund_order_id = re.id
        left join pl_eshop e on e.profile_id = re.profile_id and e.otype_id = re.otype_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=re.profile_id and bls.object_type=3 and
            re.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where re.profile_id = #{profileId} and re.refund_type in(0,1,5) and re.deleted=0 and re.refund_state
        in(0,1,2,3,5)
        <if test="eshopIds!= null and eshopIds.size()>0">
            and o.otype_id in
            <foreach collection="eshopIds" open="(" separator="," close=")" item="eshopId">
                #{eshopId}
            </foreach>
        </if>
        <if test="timeType == 0">
            and o.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 1">
            and o.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="timeType == 2">
            and o.trade_finish_time between #{beginTime} and #{endTime}
        </if>
        group by re.eshop_order_id)info group by info.id) ord on ord.profile_id = pb.profile_id and ord.buyer_id =
        pb.buyer_id
        where 1 = 1
        group by pb.buyer_id) r
        where 1 = 1
        <if test="address != null and address != '' and reportType==0">
            and r.customer_receiver_province like CONCAT('%',#{address},'%')
        </if>
        <if test="address != null and address != '' and reportType==1">
            and r.customer_receiver_city like CONCAT('%',#{address},'%')
        </if>
        <if test="reportType != null and reportType==0">
            group by r.customer_receiver_province,r.eshopId
        </if>
        <if test="reportType != null and reportType==1">
            group by r.customer_receiver_city,r.eshopId
        </if>
        ) tmp where tmp.customer_receiver_province is not null and tmp.customer_receiver_province != ''
    </select>
    <select id="querySaleOrderTradeSummaryMd5"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        select o.id,o.trade_order_id,o.otype_id,
        o.trade_modified_time,o.deleted,o.local_trade_state,
        extend.trade_summary_md5 as 'extend.trade_summary_md5',
        extend.platform_hash_order_main as 'extend.platform_hash_order_main',
        extend.platform_hash_order_detail as 'extend.platform_hash_order_detail',
        extend.platform_hash_mark_list as 'extend.platform_hash_mark_list'
        from pl_eshop_sale_order o
        left join pl_eshop_sale_order_extend extend on o.profile_id = extend.profile_id and o.id = extend.eshop_order_id
        where o.profile_id = #{profileId}
        and o.otype_id = #{otypeId}
        <if test="tradeOrderIds !=null and tradeOrderIds.size()>0">
            AND `o`.trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tradeOrderId">
                #{tradeOrderId}
            </foreach>
        </if>
    </select>
    <select id="getEshopOrderFreight"
            resultType="com.wsgjp.ct.sale.platform.dto.order.entity.EshopSaleOrderFreight">
        select *
        from pl_eshop_sale_order_freight
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
          and freight_no = #{freightNo}
          and freight_name = #{freightName}
        limit 1
    </select>

    <select id="batchQueryEshopOrderFreight"
            resultType="com.wsgjp.ct.sale.platform.dto.order.entity.EshopSaleOrderFreight">
        select f.*,o.trade_order_id
        from pl_eshop_sale_order_freight f
        join pl_eshop_sale_order o on f.profile_id=o.profile_id and f.eshop_order_id = o.id
        where f.profile_id = #{profileId} and o.otype_id=#{otypeId}
        <if test="tradeOrderList !=null and tradeOrderList.size()>0">
            AND o.trade_order_id IN
            <foreach collection="tradeOrderList" close=")" open="(" separator="," item="tradeOrderId">
                #{tradeOrderId}
            </foreach>
        </if>
    </select>

    <select id="batchGetEshopOrderFreight"
            resultType="com.wsgjp.ct.sale.platform.dto.order.entity.EshopSaleOrderFreight">
        select *
        from pl_eshop_sale_order_freight
        where profile_id = #{profileId}
          and (eshop_order_id,freight_no,freight_name) in(
        <foreach collection="params" separator="," item="param">
            (#{param.eshopOrderId},#{param.freightNo},#{param.freightName})
        </foreach>
              )
    </select>

    <select id="getAllEshopOrderFreight"
            resultType="com.wsgjp.ct.sale.platform.dto.order.entity.EshopSaleOrderFreight">
        select id,
               profile_id,
               eshop_order_id,
               freight_no,
               freight_name,
               freight_btype_id,
               freight_code,
               freight_intercept_status,
               create_time,
               update_time
        from pl_eshop_sale_order_freight
        where profile_id=#{profileId} and eshop_order_id = #{eshopOrderId}
    </select>

    <insert id="insertEshopOrderFreight">
        insert into pl_eshop_sale_order_freight(id, profile_id, eshop_order_id, freight_no, freight_name,
                                                freight_btype_id, freight_code, freight_intercept_status, create_time)
        values (#{id}, #{profileId}, #{eshopOrderId}, #{freightNo}, #{freightName}, #{freightBtypeId}, #{freightCode},
                #{freightInterceptStatus}, #{createTime})
    </insert>

    <insert id="batchInsertEshopOrderFreight">
        insert into pl_eshop_sale_order_freight
                        (id, profile_id, eshop_order_id, freight_no, freight_name,
                         freight_btype_id, freight_code, freight_intercept_status, create_time)
        values
        <foreach collection="list" item="freight" separator=",">
            (#{freight.id}, #{freight.profileId}, #{freight.eshopOrderId}, #{freight.freightNo}, #{freight.freightName},
             #{freight.freightBtypeId}, #{freight.freightCode},#{freight.freightInterceptStatus}, #{freight.createTime})
        </foreach>
    </insert>

    <update id="updateEshopOrderFreight">
        update pl_eshop_sale_order_freight
        set freight_intercept_status=#{freightInterceptStatus}
        where id = #{id};
    </update>


    <select id="queryOrderDetail" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail">
        select d.id, eshop_order_id, trade_order_detail_id, d.profile_id, d.otype_id
        from pl_eshop_sale_order_detail d
                 left join pl_eshop_sale_order o on o.profile_id = d.profile_id and o.id = d.eshop_order_id
        where d.profile_id = #{profileId}
          and d.otype_id = #{eshopId}
          and o.trade_order_id = #{tradeId}
          and d.trade_order_detail_id = #{oid}
    </select>

    <select id="querySaleOrderByPlatformStockId" resultType="java.lang.String">
        select trade_order_id
        from pl_eshop_sale_order
        where profile_id = #{profileId}
        and otype_id = #{eshopId}
        and ktype_id=0
        and platform_stock_id in
        <foreach collection="platformStockIds" open="(" separator="," close=")" item="platformStockId">
            #{platformStockId}
        </foreach>
        UNION
        select o.trade_order_id
        from pl_eshop_sale_order_detail d
        join pl_eshop_sale_order o on d.profile_id = o.profile_id and d.eshop_order_id = o.id
        where d.profile_id = #{profileId}
        and d.otype_id = #{eshopId}
        and d.ktype_id=0
        and d.platform_stock_id in
        <foreach collection="platformStockIds" open="(" separator="," close=")" item="platformStockId">
            #{platformStockId}
        </foreach>
    </select>

    <select id="querySaleOrderDetailTradeOrderDetailIds" resultType="java.lang.String">
        SELECT DISTINCT (trade_order_detail_id)
        FROM pl_eshop_sale_order_detail
        WHERE profile_id = #{profileId}
          AND otype_id = #{eshopId}
          AND id IN (SELECT id
                     FROM pl_eshop_sale_order
                     WHERE pl_eshop_sale_order.profile_id = #{profileId}
                       AND pl_eshop_sale_order.otype_id = #{eshopId}
                       AND trade_order_id = #{tradeOrderId})
    </select>

    <select id="queryOrderDetailByPlatformStockId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail">
        select d.eshop_order_id, d.id
        from pl_eshop_sale_order_detail d
                 join pl_eshop_sale_order o on o.profile_id = d.profile_id and o.id = d.eshop_order_id
        where d.profile_id = #{profileId}
          and d.otype_id = #{eshopId}
          and d.ktype_id = 0
          and o.deleted = 0
          and d.platform_stock_id = #{platformStockId}
    </select>

    <update id="deleteOrRecoveryEshopOrderByIds">
        update pl_eshop_sale_order set deleted=#{deleted} where profile_id=#{profileId}  and id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </update>

    <update id="deleteOrRecoveryEshopOrderDetailByIds">
        update pl_eshop_sale_order_detail set deleted=#{deleted} where profile_id=#{profileId} and
        eshop_order_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </update>

    <update id="updateOrderMemoAndFlagByTmc">
        update pl_eshop_sale_order
        set seller_memo=#{sellerMemo},
            seller_flag=#{sellerFlag}
        where profile_id = #{profileId}
          AND id = #{id}
    </update>

    <update id="updateOrderTradeStateByTmc">
        update pl_eshop_sale_order
        set local_trade_state=#{tradeStatus},
            trade_finish_time=#{tradeFinishTime}
        where profile_id = #{profileId}
          AND id = #{id}
    </update>

    <update id="updateOrderDetailByTmc">
        update pl_eshop_sale_order_detail
        set platform_detail_trade_state=#{tradeStatus}
        where profile_id = #{profileId}
          AND eshop_order_id = #{eshopOrderId}
    </update>

    <update id="updateAdvanceOrderMemoAndFlagByTmc">
        update td_orderbill_platform
        set seller_memo=#{sellerMemo},
            seller_flag=#{sellerFlag}
        where profile_id = #{profileId}
          AND vchcode = #{vchcode}
    </update>

    <update id="updateAdvanceOrderTradeStateByTmc">
        update td_orderbill_platform
        set local_trade_state=#{tradeStatus}
        where profile_id = #{profileId}
          AND vchcode = #{vchcode}
    </update>

    <update id="updateAdvanceOrderDetailByTmc">
        update td_orderbill_detail_platform
        set platform_detail_trade_state=#{tradeStatus}
        where profile_id = #{profileId}
          AND vchcode = #{vchcode}
    </update>

    <update id="updateOrderDetailKtypeId">
        update pl_eshop_sale_order_detail d
            join pl_eshop_sale_order o on o.profile_id = d.profile_id and o.id = d.eshop_order_id
        set d.ktype_id = #{ktypeId}
        where d.profile_id = #{profileId}
          and d.otype_id = #{eshopId}
          and d.ktype_id = 0
          and o.deleted = 0
          and d.platform_stock_id = #{platformStockId}
    </update>

    <update id="updateOrderKtype">
        update pl_eshop_sale_order
        set ktype_id=#{ktypeId},
            deliver_type=#{deliverType},
            deliver_process_type=#{deliverProcessType}
        WHERE profile_id = #{profileId}
          and id = #{id}
    </update>

    <select id="queryLocalOrderCount" resultType="int">
        select count(0)
        from pl_eshop_sale_order
        where profile_id = #{profileId}
        and create_type=1
        and otype_id in
        <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
            #{otypeId}
        </foreach>
        <choose>
            <when test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@PAY_TIME">
                and trade_pay_time between #{beginTime} and #{endTime}
            </when>
            <when test="timeType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType@DOWNLOAD_TIME">
                and create_time between #{beginTime} and #{endTime}
            </when>
            <otherwise>
                and trade_create_time between #{beginTime} and #{endTime}
            </otherwise>
        </choose>
    </select>


    <select id="queryExistOrderComboRow" resultType="java.lang.Integer">
        select count(0)
        from pl_eshop_sale_order_detail_combo
        where profile_id = #{profileId}
        and eshop_order_id = #{eshopOrderId}
        <if test="eshopOrderDetailComboRowIds!=null and eshopOrderDetailComboRowIds.size>0">
            AND eshop_order_detail_combo_row_id IN
            <foreach collection="eshopOrderDetailComboRowIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="batchQueryExistOrderComboRow" resultType="java.math.BigInteger">
        select eshop_order_detail_combo_row_id
        from pl_eshop_sale_order_detail_combo
        where profile_id = #{profileId}
        AND eshop_order_id IN
        <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="eshopOrderDetailComboRowIds!=null and eshopOrderDetailComboRowIds.size>0">
            AND eshop_order_detail_combo_row_id IN
            <foreach collection="eshopOrderDetailComboRowIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryExistDetailDistribution" resultType="java.lang.Integer">
        select count(0)
        from pl_eshop_sale_order_detail_distribution_buyer
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="batchQueryExistDetailDistribution" resultType="java.math.BigInteger">
        select eshop_order_detail_id
        from pl_eshop_sale_order_detail_distribution_buyer
        where profile_id = #{profileId}
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryExistDetailComboDistribution" resultType="java.lang.Integer">
        select count(0)
        from pl_eshop_sale_order_detail_combo_distribution_buyer
        where profile_id = #{profileId}
        and eshop_order_id = #{eshopOrderId}
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_combo_row_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="batchQueryExistDetailComboDistribution" resultType="java.math.BigInteger">
        select eshop_order_combo_row_id
        from pl_eshop_sale_order_detail_combo_distribution_buyer
        where profile_id = #{profileId}
        AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_combo_row_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryExistDetailLiveBroadcast" resultType="java.lang.Integer">
        select count(0)
        from pl_eshop_sale_order_detail_live_broadcast
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="batchQueryExistDetailLiveBroadcast" resultType="java.math.BigInteger">
        select detail_id
        from pl_eshop_sale_order_detail_live_broadcast
        where profile_id = #{profileId}
            and eshop_order_id in
        <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="queryExistDetailComboLiveBroadcast" resultType="java.lang.Integer">
        select count(0)
        from pl_eshop_sale_order_detail_combo_live_broadcast
        where profile_id = #{profileId}
        and eshop_order_id = #{eshopOrderId}
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_combo_row_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="batchQueryExistDetailComboLiveBroadcast" resultType="java.math.BigInteger">
        select eshop_order_detail_combo_row_id
        from pl_eshop_sale_order_detail_combo_live_broadcast
        where profile_id = #{profileId}
        and eshop_order_id IN
        <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_combo_row_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryExistSaleOrderDetailExtends" resultType="java.lang.Integer">
        select count(0) from pl_eshop_sale_order_detail_extend
        where profile_id=#{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id =#{eshopOrderId}
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="batchQueryExistSaleOrderDetailExtends" resultType="java.math.BigInteger">
        select id from pl_eshop_sale_order_detail_extend
        where profile_id=#{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="batchQueryExistSaleOrderDetailFreightList" resultType="java.math.BigInteger">
        select id from pl_eshop_order_detail_freight
        where
            (profile_id,trade_order_id,otype_id) IN(
        <foreach collection="list"  item="item"  separator=",">
            (#{item.profileId},#{item.tradeOrderId},#{item.otypeId})
        </foreach>
        )
    </select>

    <select id="queryExistSaleOrderDetailFreightList" resultType="java.math.BigInteger">
        select id from pl_eshop_order_detail_freight
        where profile_id=#{profileId} and otype_id=#{otypeId}
          and trade_order_id=#{tradeOrderId}  and trade_order_detail_id=#{tradeOrderDetailId}
    </select>


    <select id="batchQuerySaleOrderDetailFreightList" resultType="com.wsgjp.ct.sale.platform.dto.order.entity.EshopOrderDetailFreight">
        select id, trade_order_id, profile_id, otype_id, trade_order_detail_id, freight_name,
               freight_bill_no, extend_info, create_type, send_qty, sync_state, sync_type, has_last,
               hash_key, freight_code, create_time, update_time
        from pl_eshop_order_detail_freight
        where
        (profile_id,trade_order_id,otype_id) IN (
        <foreach collection="list" item="item" separator="," >
            (#{item.profileId},#{item.tradeOrderId},#{item.otypeId})
        </foreach>
        )
    </select>

    <select id="getSubmitedQicOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        SELECT profile_id,otype_id,trade_order_id
            FROM pl_eshop_sale_order `order`
            WHERE `order`.profile_id=#{profileId} AND `order`.process_state=1 AND `order`.create_time <![CDATA[>]]> #{time}
            AND
            EXISTS(select 1 from pl_eshop_order_mark mark
            left join mark_data md on md.profile_id = mark.profile_id and md.id = mark.mark_data_id
            where mark.profile_id = #{profileId} and mark.order_id = `order`.id and mark.mark_code=90520001 and mark.mark_target=0
              AND (mark.mark_data_id = 0 OR md.big_data = '')
            )
    </select>

</mapper>
