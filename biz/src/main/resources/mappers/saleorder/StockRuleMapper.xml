<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.StockRuleMapper">
    <insert id="insertStockRule">
        INSERT INTO `pl_eshop_stock_sync_rule` (`id`,
                                                `rule_name`,
                                                `rule_type`,
                                                `xcode`,
                                                `rule_cron`,
                                                `state`,
                                                pcategory,
                                                `profile_id`, target_type)
        VALUES (#{id},
                #{ruleName},
                #{ruleType},
                #{xcode},
                #{ruleCron},
                #{state},
                #{pcategory},
                #{profileId},
                #{targetType})
    </insert>
    <insert id="insertKtypeRule"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockRuleDetail">
        REPLACE INTO `pl_eshop_stock_sync_rule_detail` (`id`,
                                                        `rule_id`,
                                                        `ktype_id`,
                                                        `frozen_qty`,
                                                        `profile_id`, batchno, batch_price, produce_date, expire_date)
        VALUES (#{id},
                #{ruleId},
                #{ktypeId},
                #{frozenQty},
                #{profileId}, #{batchno}, #{batchPrice}, #{produceDate}, #{expireDate});
    </insert>

    <insert id="insertDefaultRule">
        REPLACE INTO `pl_eshop_stock_sync_default_rule`
        (`id`, rule_cron, zero_qty_sync_enabled, eshop_id, profile_id, ktype_ids, auto_sync_enabled,auto_sync_when_mapping_changed)
        VALUES (#{id}, #{ruleCron}, #{zeroQtySyncEnabled}, #{otypeId}, #{profileId}, #{ktypeIds}, #{autoSyncEnabled},#{autoSyncWhenMappingChanged});
    </insert>

    <insert id="saveWarehouseRule">
        REPLACE INTO pl_eshop_warehouse_stock_sync_rule
        (id, profile_id, eshop_id, warehouse_code, ktype_ids, rule_cron, zero_qty_sync_enabled, auto_sync_enabled)
        VALUES (#{id}, #{profileId}, #{otypeId}, #{warehouseCode}, #{ktypeIds}, #{ruleCron}, #{zeroQtySyncEnabled},
                #{autoSyncEnabled})
    </insert>

    <insert id="saveLadderDefaultRule">
        REPLACE INTO pl_eshop_ladder_default_sync_rule
        (id, profile_id, eshop_id, ktype_ids, rule_cron, auto_sync_ladder_qty_enabled)
        VALUES (#{id}, #{profileId}, #{otypeId}, #{ktypeIds}, #{ruleCron}, #{autoSyncLadderQtyEnabled})
    </insert>

    <insert id="saveProductSkuRuleConfig">
        REPLACE INTO pl_eshop_product_sku_rule_config
        (id, profile_id, eshop_id, warehouse_code, platform_num_id, platform_sku_id, rule_id, platform_xcode,
         target_type, platform_properties, state, uniq_mark)
        VALUES (#{id}, #{profileId}, #{eshopId}, #{warehouseCode}, #{platformNumId}, #{platformSkuId}, #{ruleId},
                #{platformXcode}, #{targetType}, #{platformProperties}, #{state}, #{uniqMark})
    </insert>


    <update id="updateStockRule">
        UPDATE
        `pl_eshop_stock_sync_rule`
        <set>
            `rule_type` = #{ruleType},
            <if test="ruleName!=null and ruleName!=''">
                `rule_name` = #{ruleName},
            </if>
            <if test="ruleCron!=null and ruleCron!=''">
                `rule_cron` = #{ruleCron},
            </if>
            <if test="xcode!=null and xcode!=''">
                `xcode`=#{xcode},
            </if>
            <if test="state!=null">
                `state` = #{state},
            </if>
            <if test="pcategory!=null">
                pcategory=#{pcategory}
            </if>
        </set>
        WHERE `id` = #{id} and profile_id=#{profileId}
    </update>

    <update id="modifySyncRuleExpand">
        replace into pl_eshop_sync_rule_expand (id, rule_id, profile_id,
        eshop_id,platfrom_num_id,platfrom_sku_id,platfrom_xcode,last_sync_time,target_type,uniq_mark)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id},#{item.ruleId},
            #{item.profileId},#{item.eshopId},#{item.platformNumId},#{item.platformSkuId},#{item.platformXcode},
            #{item.lastSyncTime},#{item.targetType},#{item.uniqMark})
        </foreach>
    </update>


    <select id="getRuleCountByRuleName" resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_stock_sync_rule
        where profile_id = #{profileId}
          and rule_name = #{ruleName}
    </select>

    <delete id="deleteRuleDetails">
        delete
        from pl_eshop_stock_sync_rule_detail
        where profile_id = #{profileId}
          and rule_id = #{ruleId}
    </delete>
    <delete id="deleteStockRulePhysics">
        delete from `pl_eshop_stock_sync_rule`
        WHERE rule_name = #{ruleName}
          AND profile_id = #{profileId}
          AND deleted = 1
    </delete>

    <update id="deleteStockRule">
        update `pl_eshop_stock_sync_rule`
        set deleted=1
        WHERE `id` = #{id}
          AND profile_id = #{profileId}
    </update>

    <update id="batchDeleteStockRule">
        update `pl_eshop_stock_sync_rule`
        set deleted=1
        WHERE profile_id = #{profileId}
        <if test="list!=null and list.size()>0">
            AND id in
            <foreach collection="list" item="item" separator="OR">
                id = #{item}
            </foreach>
        </if>
    </update>

    <delete id="deleteProductSkuRuleConfig">
        delete
        from pl_eshop_product_sku_rule_config
        where profile_id = #{profileId}
          and platform_num_id = #{numId}
          and platform_properties = #{properties}
    </delete>


    <select id="getPtypeStockRules" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        SELECT
        rule.`id`,
        rule.`rule_name`,
        rule.`xcode`,
        rule.`rule_type`,
        rule.`rule_cron`,
        rule.`state`,
        rule.pcategory,
        rule.target_type,
        ptype.id as ptypeId,
        ptype.fullname as 'ptypeName',
        ptype.batchenabled as batchEnabled,
        ptype.protect_days,
        ptype.protect_days_unit,
        expand.last_sync_time,
        bpx.sku_id,
        bpx.unit_id,
        bpu.unit_rate,
        true as autoSyncEnabled
        FROM
        `pl_eshop_stock_sync_rule` rule
        LEFT JOIN `base_ptype_xcode` bpx on bpx.profile_id=rule.profile_id and bpx.xcode=rule.xcode
        LEFT JOIN base_ptype_unit bpu on bpu.profile_id=bpx.profile_id and bpu.id=bpx.unit_id
        LEFT JOIN base_ptype_sku bps on bpx.profile_id=bps.profile_id and bpx.sku_id=bps.id
        LEFT JOIN `base_ptype` ptype on ptype.profile_id=rule.profile_id and ptype.id=bpx.ptype_id
        LEFT JOIN (select max(last_sync_time) as last_sync_time, rule_id as ruleId from `pl_eshop_sync_rule_expand` expand
        left join pl_eshop_stock_sync_rule ssr on ssr.profile_id=expand.profile_id and ssr.id=expand.rule_id
        where expand.profile_id=#{profileId}
        <if test="ruleIds!=null and ruleIds.size>0">
            And expand.rule_id IN
            <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="xcodeList!=null and xcodeList.size>0">
            and ssr.xcode IN
            <foreach collection="xcodeList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if> group by rule_id) expand on expand.ruleId=rule.id
        <if test="ktypeIds!=null and ktypeIds.size>0">
            JOIN (SELECT DISTINCT(rule_id) as ruleId FROM pl_eshop_stock_sync_rule_detail
            WHERE profile_id=#{profileId}
            And ktype_id IN
            <foreach collection="ktypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            ) d ON d.ruleId=rule.id
        </if>
        WHERE rule.profile_id=#{profileId} and rule.deleted=0
        <if test="ruleName!=null and ruleName!=''">
            AND rule_name=#{ruleName}
        </if>
        <if test="filterRuleName!=null and filterRuleName!=''">
            AND rule_name like CONCAT('%',#{filterRuleName},'%')
        </if>
        <if test="ruleType!=null and ruleType!='' and ruleType>0">
            AND rule_type=#{ruleType}
        </if>
        <if test="ruleId!=null and ruleId>0">
            AND rule.id=#{ruleId}
        </if>
        <if test="skuId!=null and skuId>0">
            AND bpx.sku_id=#{skuId}
        </if>
        <if test="unitId!=null and unitId>0">
            and bpx.unit_id=#{unitId}
        </if>
        <if test="xcode!=null and xcode!=''">
            AND rule.xcode=#{xcode}
        </if>
        <if test="pcategory!=null">
            AND rule.pcategory=#{pcategory}
        </if>
        <if test="targetType!=null and targetType!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@ALL">
            and rule.target_type=#{targetType}
        </if>
        <if test="xcodeList!=null and xcodeList.size>0">
            and rule.xcode IN
            <foreach collection="xcodeList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        and rule.deleted=0
        <if test="ruleIds!=null and ruleIds.size>0">
            AND rule.id IN
            <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="skuIdList!=null and skuIdList.size()>0">
            AND bps.id IN
            <foreach collection="skuIdList" close=")" open="(" separator="," item="sku">
                #{sku}
            </foreach>
        </if>
        order by rule_name
    </select>
    <select id="getRuleDetailList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockRuleDetail">
        SELECT
        rule.`id`,
        rule.`rule_id`,
        rule.`ktype_id`,
        rule.`frozen_qty`,
        rule.expire_date,
        rule.produce_date,
        rule.batch_price,
        rule.batchno,
        r.target_type,
        ktype.fullname
        FROM
        pl_eshop_stock_sync_rule_detail rule
        left join pl_eshop_stock_sync_rule r on rule.rule_id=r.id  and r.deleted=0
        left join `base_ktype` ktype on ktype.id=rule.ktype_id and ktype.profile_id=rule.profile_id and ktype.deleted =
        0 and ktype.stoped = 0
        where rule.profile_id=#{profileId} and r.deleted=0
        AND if(r.rule_type=3 or r.rule_type=1, ifnull(ktype.id,0), 1) >0
        <if test="ruleId!=null and ruleId>0">
            AND rule_id=#{ruleId}
        </if>
        <if test="ruleIds!=null and ruleIds.size>0">
            And rule_id IN
            <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>
    <select id="getDefaultRule" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select eshop.fullname                            as eshopName,
               eshop.eshop_type                            as eshopType,
               eshop.otype_id,
               eshop.otype_id                            as eshopId,
               eshop.profile_id,
               eshop.otype_id                            as id,
               '【默认规则】店铺同步规则'                  as ruleName,
               rule.ktype_ids,
               rule.rule_cron,
               1                                         as rule_type,
               1                                         as defaultRule,
               0                                         as targetType,
               IFNULL(rule.zero_qty_sync_enabled, false) as zeroQtySyncEnabled,
               ifnull(rule.auto_sync_enabled, false)     as autoSyncEnabled,
               ifnull(rule.auto_sync_when_mapping_changed, false)     as autoSyncWhenMappingChanged,
               ifnull(rule.warehouse_stock_sync_enabled, false) warehouseStockSyncEnabled,
               ifnull(rule.eshop_multi_stock_sync_enabled, false) eshopMultiStockSyncEnabled,
               ifnull(rule.auto_up_shelf_enabled, false) autoUpShelfEnabled
        from pl_eshop eshop
                 left join `pl_eshop_stock_sync_default_rule` rule
                           on rule.eshop_id = eshop.otype_id and rule.profile_id = eshop.profile_id
        where eshop.profile_id = #{profileId}
          and eshop.otype_id = #{eshopId}
    </select>
    <select id="getComboRules" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        SELECT
        rule.`id`,
        rule.`rule_name`,
        rule.`xcode`,
        rule.`rule_type`,
        rule.`rule_cron`,
        rule.`state`,
        rule.pcategory,
        rule.target_type,
        bp.fullname as 'ptypeName',
        bp.batchenabled as batchEnabled,
        bp.protect_days,
        bp.protect_days_unit,
        bp.id as ptypeId,
        expand.last_sync_time,
        true as autoSyncEnabled
        FROM
        `pl_eshop_stock_sync_rule` rule
        LEFT JOIN `base_ptype_xcode` bpx on bpx.profile_id=rule.profile_id and bpx.xcode=rule.xcode and bpx.info_type=1
        LEFT JOIN `base_ptype` bp on bp.profile_id=rule.profile_id and bp.id=bpx.ptype_id AND bp.`deleted`=0
        LEFT JOIN (select max(last_sync_time) as last_sync_time, expand.rule_id as ruleId from `pl_eshop_sync_rule_expand` expand
        left join pl_eshop_stock_sync_rule ssr on ssr.profile_id=expand.profile_id and ssr.id=expand.rule_id
        where expand.profile_id=#{profileId}
        <if test="ruleIds!=null and ruleIds.size>0">
            And expand.rule_id IN
            <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="xcodeList!=null and xcodeList.size>0">
            and ssr.xcode IN
            <foreach collection="xcodeList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>

        group by rule_id) expand on expand.ruleId=rule.id
        <if test="ktypeIds!=null and ktypeIds.size>0">
            JOIN (SELECT DISTINCT(rule_id) as ruleId FROM pl_eshop_stock_sync_rule_detail
            WHERE profile_id=#{profileId}
            And ktype_id IN
            <foreach collection="ktypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            ) d ON d.ruleId=rule.id
        </if>
        WHERE rule.profile_id=#{profileId} AND rule.pcategory=2 AND bp.pcategory=2 and rule.state!=1  and rule.deleted=0
        <if test="ruleName!=null and ruleName!=''">
            AND rule_name=#{ruleName}
        </if>
        <if test="filterRuleName!=null and filterRuleName!=''">
            AND rule_name like CONCAT('%',#{filterRuleName},'%')
        </if>
        <if test="xcode!=null and xcode!=''">
            and rule.xcode=#{xcode}
        </if>
        <if test="ruleType!=null and ruleType!='' and ruleType>0">
            AND rule_type=#{ruleType}
        </if>
        <if test="ruleId!=null and ruleId>0">
            AND rule.id=#{ruleId}
        </if>
        <if test="xcodeList!=null and xcodeList.size>0">
            and rule.xcode IN
            <foreach collection="xcodeList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="targetType!=null and targetType!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@ALL">
            and rule.target_type=#{targetType}
        </if>
        <if test="ruleIds!=null and ruleIds.size>0">
            And rule.id IN
            <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        order by rule_name
    </select>
    <select id="checkExistStockRules"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        SELECT
        rule.`id`,
        `rule_name`,
        rule.`xcode`,
        `rule_type`,
        `rule_cron`,
        `state`,
        rule.pcategory
        FROM
        `pl_eshop_stock_sync_rule` rule
        WHERE rule.profile_id=#{profileId} and rule.deleted = 0
        <if test="xcodeList!=null and xcodeList.size>0">
            and rule.xcode IN
            <foreach collection="xcodeList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="xcode!=null and xcode!=''">
            and rule.xcode=#{xcode}
        </if>
        <if test="ruleName!=null and ruleName!=''">
            AND rule_name=#{ruleName}
        </if>
        <if test="ruleId!=null and ruleId>0">
            AND rule.id!=#{ruleId}
        </if>
        <if test="pcategory!=null and pcategory!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.Pcategory@UnRelation">
            AND rule.pcategory=#{pcategory}
        </if>
        <if test="targetType!=null and targetType!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@ALL">
            AND rule.target_type=#{targetType}
        </if>
    </select>


    <select id="getChangeComboQty" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.ComboDetail">
        SELECT s.sku_id,
               s.ptype_id
        FROM `base_ptype` p
                 JOIN `base_ptype_combo_detail` s
                      ON s.`profile_id` = p.`profile_id`
                          AND s.`combo_id` = p.`id`
        WHERE p.profile_id = #{profileId}
          AND p.id = #{comboId}
    </select>


    <select id="getAllStockRules" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        SELECT
        rule.`id`,
        rule.`rule_name`,
        rule.`xcode`,
        rule.`rule_type`,
        rule.`rule_cron`,
        rule.`state`,
        rule.pcategory,
        rule.target_type,
        ptype.fullname as 'ptypeName',
        ptype.batchenabled as batchEnabled,
        ptype.protect_days,
        ptype.protect_days_unit,
        bpx.ptype_id,
        bpx.sku_id,
        bpu.id as unitId,
        bpu.unit_rate,
        expand.last_sync_time,
        true as autoSyncEnabled
        FROM
        `pl_eshop_stock_sync_rule` rule
        LEFT JOIN `base_ptype_xcode` bpx on bpx.profile_id=rule.profile_id and bpx.xcode=rule.xcode and bpx.info_type=0
        LEFT JOIN base_ptype_unit bpu on bpu.profile_id=bpx.profile_id and bpu.id=bpx.unit_id
        LEFT JOIN `base_ptype` ptype on ptype.profile_id=rule.profile_id and ptype.id=bpx.ptype_id
        LEFT JOIN (select max(last_sync_time) as last_sync_time, rule_id as ruleId from `pl_eshop_sync_rule_expand` expand
                    left join pl_eshop_stock_sync_rule ssr on ssr.profile_id=expand.profile_id and ssr.id=expand.rule_id and ssr.deleted=0
        where expand.profile_id=#{profileId}
        <if test="ruleIds!=null and ruleIds.size>0">
            And expand.rule_id IN
            <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="xcodeList!=null and xcodeList.size>0">
            and ssr.xcode IN
            <foreach collection="xcodeList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        group by expand.rule_id)
        expand on expand.ruleId=rule.id
        <if test="ktypeIds!=null and ktypeIds.size>0">
            JOIN (SELECT DISTINCT(rule_id) rule_id FROM pl_eshop_stock_sync_rule_detail
            WHERE profile_id=#{profileId}
            And ktype_id IN
            <foreach collection="ktypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            ) d ON d.rule_id=rule.id
        </if>
        WHERE rule.profile_id=#{profileId} and rule.pcategory=0 and rule.deleted=0
        <if test="ruleName!=null and ruleName!=''">
            AND rule_name=#{ruleName}
        </if>
        <if test="filterRuleName!=null and filterRuleName!=''">
            AND rule_name like CONCAT('%',#{filterRuleName},'%')
        </if>
        <if test="ruleType!=null and ruleType!='' and ruleType>0">
            AND rule_type=#{ruleType}
        </if>
        <if test="ruleId!=null and ruleId>0">
            AND rule.id=#{ruleId}
        </if>
        <if test="skuId!=null and skuId>0">
            and bpx.sku_id=#{skuId}
        </if>
        <if test="unitId!=null and unitId>0">
            and bpx.unit_id=#{unitId}
        </if>
        <if test="xcode!=null and xcode!=''">
            and rule.xcode=#{xcode}
        </if>
        <if test="pcategory!=null">
            AND rule.pcategory=#{pcategory}
        </if>
        <if test="targetType!=null and targetType!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@ALL">
            and rule.target_type=#{targetType}
        </if>
        <if test="xcodeList!=null and xcodeList.size>0">
            and rule.xcode IN
            <foreach collection="xcodeList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="ruleIds!=null and ruleIds.size>0">
            And rule.id IN
            <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        union all
        SELECT
        rule.`id`,
        `rule_name`,
        rule.`xcode`,
        `rule_type`,
        `rule_cron`,
        `state`,
        rule.pcategory,
        rule.target_type,
        bp.fullname as 'ptypeName',
        bp.batchenabled as batchEnabled,
        bp.protect_days,
        bp.protect_days_unit,
        bp.id as ptypeId,
        0 as skuId,
        0 as unitId,
        1 as unitRate,
        expand.last_sync_time,
        true as autoSyncEnabled
        FROM
        `pl_eshop_stock_sync_rule` rule
        left join `base_ptype_xcode` bpx on bpx.profile_id=rule.profile_id and bpx.xcode=rule.xcode and bpx.info_type=1
        LEFT JOIN `base_ptype` bp on bp.profile_id=bpx.profile_id and
        bp.id=bpx.ptype_id AND bp.`deleted`=0
        LEFT JOIN (select max(last_sync_time) as last_sync_time, rule_id as ruleId from `pl_eshop_sync_rule_expand` expand
        left join pl_eshop_stock_sync_rule ssr on ssr.profile_id=expand.profile_id and ssr.id=expand.rule_id
        where expand.profile_id=#{profileId}
        <if test="ruleIds!=null and ruleIds.size>0">
            And expand.rule_id IN
            <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="xcodeList!=null and xcodeList.size>0">
            and ssr.xcode IN
            <foreach collection="xcodeList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        group by rule_id) expand on expand.ruleId=rule.id
        <if test="ktypeIds!=null and ktypeIds.size>0">
            JOIN (SELECT DISTINCT(rule_id) rule_id FROM pl_eshop_stock_sync_rule_detail
            WHERE profile_id=#{profileId}
            And ktype_id IN
            <foreach collection="ktypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            ) d ON d.rule_id=rule.id
        </if>
        WHERE rule.profile_id=#{profileId} AND rule.pcategory=2 AND bp.pcategory=2 and rule.state!=1 and rule.deleted=0
        <if test="ruleName!=null and ruleName!=''">
            AND rule_name=#{ruleName}
        </if>
        <if test="filterRuleName!=null and filterRuleName!=''">
            AND rule_name like CONCAT('%',#{filterRuleName},'%')
        </if>
        <if test="xcode!=null and xcode!=''">
            and rule.xcode=#{xcode}
        </if>
        <if test="ruleType!=null and ruleType!='' and ruleType>0">
            AND rule_type=#{ruleType}
        </if>
        <if test="targetType!=null and targetType!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@ALL">
            and rule.target_type=#{targetType}
        </if>
        <if test="ruleId!=null and ruleId>0">
            AND rule.id=#{ruleId}
        </if>
        <if test="xcodeList!=null and xcodeList.size>0">
            and rule.xcode IN
            <foreach collection="xcodeList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="ruleIds!=null and ruleIds.size>0">
            And rule.id IN
            <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        order by rule_name
    </select>

    <select id="queryDefaultRules" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select eshop.fullname as eshopName,eshop.otype_id,eshop.profile_id,eshop.eshop_type,
        eshop.otype_id as id,
        0 as targetType,
        ifnull(rule.ktype_ids, bo.ktype_id) as ktype_ids,
        IFNULL(rule.zero_qty_sync_enabled, false) as zeroQtySyncEnabled,
        rule.rule_cron,
        ifnull(rule.auto_sync_enabled, false) autoSyncEnabled,
        ifnull(rule.warehouse_stock_sync_enabled, false) warehouseStockSyncEnabled,
        ifnull(rule.eshop_multi_stock_sync_enabled, false) eshopMultiStockSyncEnabled,
        ifnull(rule.auto_up_shelf_enabled, false) autoUpShelfEnabled
        from pl_eshop eshop
        left join base_otype bo on bo.id= eshop.otype_id AND bo.`profile_id`=eshop.`profile_id`
        left join `pl_eshop_stock_sync_default_rule` rule on rule.eshop_id=eshop.otype_id and
        rule.profile_id=eshop.profile_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        <where>
            eshop.profile_id=#{profileId}
            and eshop.deleted =0
            and eshop.stoped =0
            and bo.deleted=0
            and bo.stoped=0
            <if test="eshopId!=null and eshopId>0">
                and eshop.otype_id=#{eshopId}
            </if>
            <if test="syncStockType>-1">
                and ifnull(rule.auto_sync_enabled, 0)=#{syncStockType}
            </if>
            <if test="ktypeIds!=null and ktypeIds.size()>0">
                and(
                <foreach item="item" index="index" collection="ktypeIds" separator="or">
                    FIND_IN_SET(#{item},ifnull(rule.ktype_ids,bo.ktype_id))
                </foreach>
                )
            </if>
            <if test="eshopName!=null and eshopName!=''">
                and eshop.fullname like CONCAT('%',#{eshopName},'%')
            </if>
            <if test="autoSyncEnabled!=null">
                and ifnull(rule.auto_sync_enabled, false)=#{autoSyncEnabled}
            </if>

            <if test="shopTypes!=null and shopTypes.size()>0">
                and eshop.eshop_type in
                <foreach collection="shopTypes" close=")" open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        order by bo.rowindex
    </select>

    <select id="queryCustomRuleList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select id, rule_name, rule_type, rule_cron, state, profile_id, zero_qty_sync_enabled, warehouse_stock_sync_enabled, eshop_multi_stock_sync_enabled, auto_up_shelf_enabled
        from pl_eshop_stock_sync_rule
        where profile_id=#{profileId}
        <if test="ruleName!=null and ruleName!=''">
            and rule_name like CONCAT('%',#{ruleName},'%')
        </if>
        <if test="ktypeIds!=null and ktypeIds.size()>0">
            and id in (
                select rule_id from pl_eshop_stock_sync_rule_detail
                               where  profile_id=#{profileId}
                               and ktype_id in
                                <foreach item="kid" index="index" open="(" separator="," close=")" collection="ktypeIds" >
                                    #{kid}
                                </foreach>
            )
        </if>
    </select>

    <select id="queryWarehouseRules" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select bo.fullname as eshopName,bo.id as otypeId,bo.profile_id,eshop.eshop_type,
        ifnull(rule.id, sp.id) as id, rule.ktype_ids,
        IFNULL(rule.zero_qty_sync_enabled,false) as zeroQtySyncEnabled,
        rule.rule_cron,
        1 as targetType,
        ifnull(rule.auto_sync_enabled,false) as autoSyncEnabled,
        sp.platform_store_stock_id as warehouseCode,sp.platform_store_name as warehouseName
        from pl_eshop_platform_store_mapping sp
        left join base_otype bo on sp.profile_id = bo.profile_id and sp.eshop_id=bo.id
        left join pl_eshop eshop on sp.profile_id = eshop.profile_id and sp.eshop_id=eshop.otype_id
        left join pl_eshop_warehouse_stock_sync_rule rule on rule.profile_id=sp.profile_id and
        sp.platform_store_stock_id=rule.warehouse_code and rule.eshop_id=sp.eshop_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where sp.profile_id=#{profileId}
        and bo.deleted =0
        and bo.stoped=0
        and bo.ocategory in (0,1,2)
        and (sp.type=0 or (sp.type=1 and sp.platform_store_type=3))
        <if test="eshopId!=null and eshopId>0">
            and sp.eshop_id=#{eshopId}
        </if>
        <if test="warehouseCode!=null and warehouseCode!=''">
            and sp.platform_store_stock_id=#{warehouseCode}
        </if>
        <if test="syncStockType==0">
            and IFNULL(rule.auto_sync_enabled,FALSE)=false
        </if>
        <if test="syncStockType==1">
            and rule.auto_sync_enabled=true
        </if>
        <if test="filterString!=null and filterString!=''">
            and (sp.platform_store_stock_id like CONCAT('%',#{filterString},'%') or platform_store_name like
            CONCAT('%',#{filterString},'%'))
        </if>
        <if test="ktypeIds!=null and ktypeIds.size()>0">
            and(rule.ktype_ids='' OR rule.`ktype_ids` IS NULL
            <foreach item="item" index="index" open="or" collection="ktypeIds" separator="or">
                FIND_IN_SET(#{item},rule.ktype_ids)
            </foreach>
            )
        </if>
        order by bo.rowindex
    </select>
    <select id="getWarehouseRule" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select bo.fullname                               as eshopName,
               pe.eshop_type                             as eshopType,
               bo.id                                     as otypeId,
               bo.profile_id,
               ifnull(rule.id, sp.id)                    as id,
               rule.ktype_ids,
               IFNULL(rule.zero_qty_sync_enabled, false) as zeroQtySyncEnabled,
               rule.rule_cron,
               1                                         as targetType,
               ifnull(rule.auto_sync_enabled, false)     as autoSyncEnabled,
               sp.platform_store_stock_id                as warehouseCode,
               sp.platform_store_name                    as warehouseName
        from pl_eshop_platform_store_mapping sp
                 left join base_otype bo
                           on sp.profile_id = bo.profile_id and sp.eshop_id = bo.id
            left join pl_eshop pe
                           on pe.profile_id = bo.profile_id and pe.otype_id = bo.id
                 left join pl_eshop_warehouse_stock_sync_rule rule
                           on rule.profile_id = sp.profile_id
                               and sp.platform_store_stock_id = rule.warehouse_code
                               and rule.eshop_id = sp.eshop_id
        where sp.profile_id = #{profileId}
          and sp.eshop_id = #{otypeId}
          and (sp.type = 0 or (sp.type=1 and sp.platform_store_type=3))
          and sp.platform_store_stock_id = #{warehouseCode}
        limit 1
    </select>

    <select id="queryLadderDefaultRules" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select eshop.fullname as eshopName,eshop.otype_id,eshop.profile_id,
        ifnull(rule.id,eshop.otype_id-1) as id,
        2 as targetType,
        rule.ktype_ids,
        IFNULL(deft.zero_qty_sync_enabled, false) as zeroQtySyncEnabled,rule.rule_cron,
        ifnull(deft.auto_sync_enabled, false) as autoSyncEnabled,
        ifnull(rule.auto_sync_ladder_qty_enabled, false) as autoSyncLadderQtyEnabled
        from pl_eshop eshop
        left join base_otype bo on bo.id= eshop.otype_id AND bo.`profile_id`=eshop.`profile_id`
        left join pl_eshop_ladder_default_sync_rule rule on rule.eshop_id=eshop.otype_id and
        rule.profile_id=eshop.profile_id
        left join pl_eshop_stock_sync_default_rule deft on deft.profile_id=eshop.profile_id and
        deft.eshop_id=eshop.otype_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        <where>
            eshop.profile_id=#{profileId}
            and eshop.deleted =0
            and eshop.stoped =0
            and bo.deleted=0
            and bo.stoped=0
            <if test="eshopId!=null and eshopId>0">
                and eshop.otype_id=#{eshopId}
            </if>
            <if test="syncStockType>-1">
                and ifnull(rule.auto_sync_enabled, 0)=#{syncStockType}
            </if>
            <if test="ktypeIds!=null and ktypeIds.size()>0">
                and(rule.ktype_ids='' OR rule.`ktype_ids` IS NULL
                <foreach item="item" index="index" open="or" collection="ktypeIds" separator="or">
                    FIND_IN_SET(#{item},rule.ktype_ids)
                </foreach>
                )
            </if>
            <if test="shopTypes!=null and shopTypes.size()>0">
                and eshop.eshop_type in
                <foreach collection="shopTypes" close=")" open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        order by bo.rowindex
    </select>
    <select id="getLadderDefaultRule" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select eshop.fullname                      as eshopName,
               eshop.eshop_type                            as eshopType,
               eshop.otype_id,
               eshop.otype_id                      as eshopId,
               eshop.profile_id,
               ifnull(rule.id, eshop.otype_id - 1) as id,
               '【默认规则】阶梯库存同步规则'        as ruleName,
               rule.ktype_ids,
               rule.rule_cron,
               rule.auto_sync_ladder_qty_enabled,
               1                                   as rule_type,
               1                                   as defaultRule,
               2                                   as targetType
        from pl_eshop eshop
                 left join pl_eshop_ladder_default_sync_rule rule
                           on rule.eshop_id = eshop.otype_id and rule.profile_id = eshop.profile_id
        where eshop.profile_id = #{profileId}
          and eshop.otype_id = #{otypeId}
    </select>
    <select id="queryAutoSyncDefaultRules"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select id,
               rule_cron,
               zero_qty_sync_enabled,
               eshop_id as otypeId,
               profile_id,
               ktype_ids,
               auto_sync_enabled,
               1        as ruleType,
               ''       as warehouseCode,
               1        as warehouseType,
               0        as targetType,
               warehouse_stock_sync_enabled,
               eshop_multi_stock_sync_enabled,
               auto_up_shelf_enabled
        FROM pl_eshop_stock_sync_default_rule
        where profile_id = #{profileId}
        <if test="eshopIds!=null and eshopIds.size()>0">
            and eshop_id in
            <foreach collection="eshopIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="needAutoSync!=null and needAutoSync">
            and auto_sync_enabled = 1
        </if>
        union
        SELECT r.id,
               rule_cron,
               zero_qty_sync_enabled,
               r.eshop_id as otypeId,
               r.profile_id,
               ktype_ids,
               auto_sync_enabled,
               1          as ruleType,
               warehouse_code,
               ps.type    as warehouseType,
               1          as targetType,
               0 as warehouse_stock_sync_enabled,
               0 as eshop_multi_stock_sync_enabled,
               0 as auto_up_shelf_enabled
        FROM pl_eshop_warehouse_stock_sync_rule r
                 left join pl_eshop_platform_store_mapping ps
                           on ps.profile_id = r.profile_id and ps.eshop_id = r.eshop_id and r.warehouse_code = ps.platform_store_stock_id
        where r.profile_id = #{profileId}
        <if test="eshopIds!=null and eshopIds.size()>0">
            and r.eshop_id in
            <foreach collection="eshopIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="needAutoSync!=null and needAutoSync">
            and r.auto_sync_enabled = 1
        </if>
        and (ps.type=0 or (ps.type=1 and ps.platform_store_type=3))
    </select>


    <select id="getProductSkuRuleConfig"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.EshopProductSkuRuleConfig">
        select id, profile_id, eshop_id, warehouse_code, platform_num_id, platform_sku_id, rule_id, platform_xcode,
        target_type, platform_properties, uniq_mark, state
        from pl_eshop_product_sku_rule_config
        where profile_id=#{profileId}
        and eshop_id=#{eshopId}
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and platform_num_id in
            <foreach item="item" index="index" open="(" close=")" collection="platformNumIds" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="properties!=null and properties.size()>0">
            and platform_properties in
            <foreach item="item" index="index" open="(" close=")" collection="properties" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="targetType!=null and targetType!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@ALL">
            and target_type=#{targetType}
        </if>
    </select>

    <select id="getSimpleDefault" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select id,
               rule_cron,
               zero_qty_sync_enabled,
               eshop_id,
               profile_id,
               create_time,
               update_time,
               ktype_ids,
               auto_sync_enabled
        from pl_eshop_stock_sync_default_rule
        where profile_id = #{profileId}
          and id = #{id}
    </select>

    <select id="getSimpleWarehouseRule" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select id,
               profile_id,
               eshop_id,
               warehouse_code,
               ktype_ids,
               rule_cron,
               zero_qty_sync_enabled,
               create_time,
               update_time,
               auto_sync_enabled
        from pl_eshop_warehouse_stock_sync_rule
        where profile_id = #{profileId}
          and id = #{id}
    </select>

    <select id="queryRuleRelationCount" resultType="java.lang.Integer">
        select count(1)
        from pl_eshop_product_sku_rule_config rc
                 left join pl_eshop_config ec on rc.eshop_id = ec.eshop_id and rc.profile_id = ec.profile_id
                 left join pl_eshop_product_sku sku on sku.eshop_id = rc.eshop_id and sku.profile_id = rc.profile_id and
                                                       sku.platform_num_id = rc.platform_num_id and
                                                       sku.platform_sku_id = rc.platform_sku_id
        where rc.profile_id = #{profileId}
          and rc.rule_id = #{id}
          and ec.mapping_type = 0
        and sku.id is not null
    </select>

    <select id="querySkuMappingList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select peps.platform_num_id, peps.platform_xcode, peps.platform_sku_id, peps.eshop_id, peps.profile_id
        from pl_eshop_product_sku peps
                 left join pl_eshop_product_sku_expand ex
                           on peps.profile_id = ex.profile_id and peps.eshop_id = ex.eshop_id and
                              peps.unique_id = ex.unique_id
        where peps.profile_id = #{profileId}
          and peps.eshop_id = #{eshopId}
          and peps.platform_xcode = #{xcode}
          and ex.mapping_type = 1
    </select>

    <select id="queryRecordDetails" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select trade_id,
               ptype_id,
               sku_id,
               ktype_id,
               rule_id,
               qty,
               batchno,
               produce_date,
               expire_date,
               batch_price
        from stock_record_order_detail
        where profile_id = #{profileId}
          and rule_id = #{ruleId}
          and trade_id = #{tradeId}
          and source_type = 1
    </select>


    <select id="queryUseRuleEshop" resultType="java.math.BigInteger">
        select pepsrc.eshop_id
        from pl_eshop_product_sku_rule_config pepsrc
                 left join pl_eshop_product_sku peps
                           on pepsrc.profile_id = peps.profile_id and pepsrc.platform_num_id = peps.platform_num_id and
                              pepsrc.platform_sku_id = peps.platform_sku_id
                 left join pl_eshop_product_sku_expand exd
                           on peps.profile_id = exd.profile_id and peps.unique_id = exd.unique_id
        where pepsrc.profile_id = #{profileId}
          and pepsrc.rule_id = #{ruleId}
          and peps.id > 0
          and exd.mapping_type = 0
        union
        select ps.eshop_id
        from pl_eshop_stock_sync_rule r
                 left join pl_eshop_product_sku ps on ps.profile_id = r.profile_id and r.xcode = ps.platform_xcode
                 left join pl_eshop_product_sku_expand pex
                           on ps.profile_id = pex.profile_id and ps.unique_id = pex.unique_id
        where r.profile_id = #{profileId} and r.deleted=0
          and r.id = #{ruleId}
          and r.deleted=0
          and pex.mapping_type = 1
          and ps.id > 0
    </select>
    <select id="queryProductRules"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.ProductStockSyncRule">
        select psc.id,psc.profile_id,psc.eshop_id,psc.ptype_id,p.fullname,p.usercode,pf.fullbarcode as fullBarcode,ps.prop_names as prop,ps.propvalue_names as propName,
        px.xcode,pu.unit_name ,p.standard,p.ptype_type as type,pb.brand_name as brand from
        pl_eshop_ptype_stock_sync_rule_config psc
        left join base_ptype p  on p.profile_id=psc.profile_id and p.id=psc.ptype_id
        left join base_brandtype pb  on pb.profile_id=psc.profile_id and pb.id=p.brand_id
        left join base_ptype_sku ps on ps.profile_id=psc.profile_id and ps.id=psc.sku_id
        left join base_ptype_fullbarcode pf on pf.profile_id=psc.profile_id  and pf.ptype_id=psc.ptype_id and  pf.sku_id=psc.sku_id and pf.unit_id=psc.unit_id and pf.defaulted=1
        left join base_ptype_unit pu on pu.profile_id=psc.profile_id and pu.ptype_id=psc.ptype_id and pu.id=psc.unit_id
        left join base_ptype_xcode px on px.profile_id=psc.profile_id and psc.ptype_id=px.ptype_id and px.sku_id=psc.sku_id and px.unit_id=psc.unit_id and px.defaulted=1
        where psc.profile_id=#{profileId} and p.deleted=false
        <if test="filterList!=null and filterList.size()>0">
            and
            <foreach collection="filterList" index="index" item="p" open="(" separator="or" close=")">
                p.fullname like  CONCAT('%',#{p},'%')
           or p.usercode like CONCAT('%',#{p},'%')
           or px.xcode like CONCAT('%',#{p},'%')
           or ps.propvalue_names like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="skuUniqueIdList!=null and skuUniqueIdList.size()>0">
            and psc.sku_unique_id in
            <foreach collection="skuUniqueIdList" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        </if>
         order by psc.create_time desc
        </select>
    <select id="queryProductBaseRules"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.ProductStockSyncRuleBase">
        select psc.id,psc.profile_id,psc.eshop_id,psc.ptype_id,psc.ptype_id,psc.sku_id,psc.unit_id from
        pl_eshop_ptype_stock_sync_rule_config psc
        left join base_ptype bp on bp.profile_id=psc.profile_id and bp.id=psc.ptype_id
        where psc.profile_id=#{profileId} and bp.pcategory <![CDATA[<>]]> 2
        <if test="skuList!=null and skuList.size()>0">
            and psc.sku_id in
            <foreach collection="skuList" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        </if>
        union
        select distinct psc.id,psc.profile_id,psc.eshop_id,psc.ptype_id,psc.ptype_id,psc.sku_id,psc.unit_id from
        pl_eshop_ptype_stock_sync_rule_config psc
        left join base_ptype bp on bp.profile_id=psc.profile_id and bp.id=psc.ptype_id
        left join base_ptype_combo_detail bpcd on bpcd.profile_id=psc.profile_id and bpcd.combo_id=psc.ptype_id
        where psc.profile_id=#{profileId} and bp.pcategory = 2
        <if test="skuList!=null and skuList.size()>0">
            and bpcd.sku_id in
            <foreach collection="skuList" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        </if>
    </select>
    <insert id="addProductRules">
        replace into pl_eshop_ptype_stock_sync_rule_config(id,profile_id,eshop_id,ptype_id,sku_id,unit_id,sku_unique_id,stock_sync_enabled,create_time,update_time)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id},#{item.profileId},
            #{item.eshopId},#{item.ptypeId},#{item.skuId},#{item.unitId},#{item.skuUniqueId},
            #{item.stockSyncEnabled},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>
    <delete id="deleteProductRules">
        delete from pl_eshop_ptype_stock_sync_rule_config where id in
        <foreach collection="list" separator="," item="item" close=")" open="(">
            #{item.id}
        </foreach>
    </delete>


    <select id="getAllStockSyncConfigs" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncConfig">
        select cfg.id, cfg.fullname,cfg.config_code,cfg.profile_id,cfg.limit_ktype_ids,cfg.qty_type,cfg.static_qty,cfg.rule_cron,
               cfg.creator_id,be.fullname as etypeName,cfg.create_time
        <include refid="QueryStockSyncConfigs" />
        order by cfg.config_code desc
    </select>

    <select id="getAllStockSyncConfigsCount" resultType="java.lang.Integer">
        select count(1)
        <include refid="QueryStockSyncConfigs" />
        order by cfg.config_code
    </select>

    <sql id="QueryStockSyncConfigs">
        from pl_eshop_stock_sync_config cfg
        LEFT JOIN base_etype be on be.profile_id = cfg.profile_id and be.id = cfg.creator_id
        <if test="etypeLimited">
            inner join base_limit_scope blse on blse.profile_id=cfg.profile_id and blse.object_type=1 and
            blse.object_id = #{etypeId}
        </if>
        <where>
            cfg.profile_id=#{profileId}
            <if test="sceneType != null">
                AND EXISTS(SELECT 1
                FROM `pl_eshop_stock_sync_config_apply` apply
                WHERE apply.config_id = cfg.id and apply.scene_type= #{sceneType})
            </if>
            <if test="fullname!=null and fullname!=''">
                and  cfg.fullname like  CONCAT('%',#{fullname},'%')
            </if>
            <if test="configCode!=null and configCode!=''">
                and  cfg.config_code like  CONCAT('%',#{configCode},'%')
            </if>
            <if test="etypeName!=null and etypeName!=''">
                and  be.fullname like  CONCAT('%',#{etypeName},'%')
            </if>
            <if test="createTimeBegin!=null">
                and cfg.create_time >= #{createTimeBegin}
            </if>
            <if test="createTimeEnd != null">
                and cfg.create_time <![CDATA[<=]]>  #{createTimeEnd}
            </if>
            <if test="autoSyncEnabled != null">
                AND EXISTS(SELECT 1
                FROM `pl_eshop_stock_sync_config_apply` apply
                WHERE apply.config_id = cfg.id and apply.auto_sync_enabled= #{autoSyncEnabled})
            </if>
            <if test="otypeIds!=null and otypeIds.size()>0">
                AND EXISTS(SELECT 1
                FROM `pl_eshop_stock_sync_config_apply` apply
                WHERE apply.config_id = cfg.id and apply.eshop_id in
                <foreach collection="otypeIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                )
            </if>
            <if test="ktypeIds!=null and ktypeIds.size()>0">
                and
                <foreach collection="ktypeIds" separator=" or " item="item" close=")" open="(">
                    cfg.limit_ktype_ids  like  CONCAT('%',#{item},'%')
                </foreach>
            </if>
            <if test="sceneTypes!=null and sceneTypes.size()>0">
                AND EXISTS(SELECT 1
                FROM `pl_eshop_stock_sync_config_apply` apply
                WHERE apply.config_id = cfg.id and apply.scene_type in
                <foreach collection="sceneTypes" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                )
            </if>
        </where>
    </sql>



    <insert id="insertStockSyncConfig">
        INSERT INTO `pl_eshop_stock_sync_config` (`id`,
                                                `fullname`,
                                                `profile_id`,
                                                `limit_ktype_ids`,
                                                `qty_type`,
                                                `static_qty`,
                                                `rule_cron`,
                                                `creator_id`, `config_code`)
        VALUES (#{id},
                #{fullname},
                #{profileId},
                #{limitKtypeIds},
                #{qtyType},
                #{staticQty},
                #{ruleCron},
                #{creatorId},
                #{configCode})
    </insert>

    <update id="updateStockSyncConfig">
        UPDATE
        `pl_eshop_stock_sync_config`
        <set>
            `qty_type` = #{qtyType},
            `static_qty` = #{staticQty},
            <if test="fullname!=null and fullname!=''">
                `fullname` = #{fullname},
            </if>
            <if test="limit_ktype_ids!=null and limit_ktype_ids!=''">
                `limit_ktype_ids` = #{limitKtypeIds},
            </if>
            <if test="ruleCron!=null and ruleCron!=''">
                `rule_cron`=#{ruleCron},
            </if>
            <if test="configCode!=null and configCode!=''">
                `config_code`=#{configCode},
            </if>
            `creator_id` = #{creatorId}
        </set>
        WHERE `id` = #{id} and profile_id=#{profileId}
    </update>

    <delete id="deleteStockSyncConfigs">
        delete
        from pl_eshop_stock_sync_config
        where profile_id = #{profileId}
        and id in
        <foreach collection="ids" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </delete>

    <select id="getAllStockSyncConfigApplys" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncConfigApply">
        select apply.id, apply.profile_id,apply.eshop_id,apply.platform_stock_code,apply.platform_sku_unique_id,apply.platform_xcode,apply.ptype_id,
        apply.sku_id,apply.unit_id,apply.sync_qty_target_type,apply.config_id,apply.zero_qty_sync_enabled,apply.auto_sync_enabled,
        apply.auto_sync_when_mapping_changed,apply.scene_type,apply.sync_time_type,apply.unique_id,
        batchInfo.id ,batchInfo.apply_id,batchInfo.profile_id,batchInfo.ktype_id,batchInfo.ptype_id,batchInfo.sku_id,batchInfo.batchno,
        batchInfo.batch_price,batchInfo.produce_date,batchInfo.expire_date
        from pl_eshop_stock_sync_config_apply apply
        LEFT JOIN pl_eshop_stock_sync_config_limit_batch batchInfo on apply.profile_id  = batchInfo.profile_id and apply.id  = batchInfo.apply_id
        <where>
            apply.profile_id=#{profileId}
            <if test="configIds!=null and configIds.size()>0">
                and apply.config_id in
                <foreach collection="configIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="sceneType!=null">
                and apply.scene_type = #{sceneType}
            </if>
            <if test="otypeIds!=null and otypeIds.size()>0">
                and apply.eshop_id in
                <foreach collection="otypeIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="configIds!=null and configIds.size()>0">
                and apply.config_id in
                <foreach collection="configIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>

        </where>
    </select>


    <insert id="batchInsertStockSyncConfigApplys">
        INSERT INTO pl_eshop_stock_sync_config_apply
        (`id`,
        `profile_id`,
        `eshop_id`,
        `platform_stock_code`,
        `platform_sku_unique_id`,
        `platform_xcode`,
        `ptype_id`,
        `sku_id`, `unit_id`, `sync_qty_target_type`, `config_id`,
        `zero_qty_sync_enabled`, `auto_sync_enabled`,
        `auto_sync_when_mapping_changed`,`scene_type`,
         `sync_time_type`,`unique_id`)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id},
            #{item.profileId},
            #{item.eshopId},
            #{item.platformStockCode},
            #{item.platformSkuUniqueId},
            #{item.platformXcode},
            #{item.ptypeId},
            #{item.skuId},
            #{item.unitId},
            #{item.syncQtyTargetType},
            #{item.configId},#{item.zeroQtySyncEnabled},
            #{item.autoSyncEnabled},#{item.autoSyncWhenMappingChanged},#{item.sceneType},
            #{item.syncTimeType},#{item.uniqueId}
          )
        </foreach>
    </insert>

    <insert id="insertStockSyncConfigApply">
        INSERT INTO `pl_eshop_stock_sync_config_apply` (`id`,
                                                  `profile_id`,
                                                  `eshop_id`,
                                                  `platform_stock_code`,
                                                  `platform_sku_unique_id`,
                                                  `platform_xcode`,
                                                  `ptype_id`,
                                                  `sku_id`, `unit_id`, `sync_qty_target_type`, `config_id`,
                                                  `zero_qty_sync_enabled`, `auto_sync_enabled`,
                                                  `auto_sync_when_mapping_changed`,`scene_type`,`sync_time_type`,`unique_id`)
        VALUES (#{id},
                #{profileId},
                #{eshopId},
                #{platformStockCode},
                #{platformSkuUniqueId},
                #{platformXcode},
                #{ptypeId},
                #{skuId},
                #{unitId},
                #{syncQtyTargetType},
                #{configId},#{zeroQtySyncEnabled},
                #{autoSyncEnabled},#{autoSyncWhenMappingChanged},#{sceneType},
                #{syncTimeType},#{uniqueId})
    </insert>

    <update id="updateStockSyncConfigApply">
        UPDATE
        `pl_eshop_stock_sync_config_apply`
        <set>
            `eshop_id` = #{eshopId},
            <if test="platformStockCode!=null and platformStockCode!=''">
                `platform_stock_code` = #{platformStockCode},
            </if>
            <if test="PlatformSkuUniqueId!=null and PlatformSkuUniqueId!=''">
                `platform_sku_unique_id` = #{platformSkuUniqueId},
            </if>
            <if test="platformXcode!=null and platformXcode!=''">
                `platform_xcode`=#{platformXcode},
            </if>
            `ptype_id` = #{ptypeId},
            `sku_id` = #{skuId},`unit_id` = #{unitId},`sync_qty_target_type` = #{syncQtyTargetType},
            `config_id` = #{configId},`zero_qty_sync_enabled` = #{zeroQtySyncEnabled},`auto_sync_enabled` = #{autoSyncEnabled},
            `auto_sync_when_mapping_changed` = #{autoSyncWhenMappingChanged},`scene_type`=#{sceneType},`sync_time_type` = #{syncTimeType},`unique_id` = #{uniqueId}
        </set>
        WHERE `id` = #{id} and profile_id=#{profileId}
    </update>

    <delete id="deleteStockSyncConfigApplys">
        delete
        from pl_eshop_stock_sync_config_apply
        where profile_id = #{profileId}
            and unique_id in
            <foreach collection="uniqueIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
    </delete>

    <insert id="insertStockSyncConfigApplyLimitBatch">
        INSERT INTO `pl_eshop_stock_sync_config_limit_batch` (`id`,
                                                        `apply_id`,
                                                        `profile_id`,
                                                        `ktype_id`,
                                                        `ptype_id`,
                                                        `sku_id`,
                                                        `batchno`,
                                                        `batch_price`, `produce_date`, `expire_date`)
        VALUES (#{id},
                #{applyId},
                #{profileId},
                #{ktypeId},
                #{ptypeId},
                #{skuId},
                #{batchno},
                #{batchPrice},
                #{produceDate},
                #{expireDate})
    </insert>

    <insert id="batchinsertStockSyncConfigApplyLimitBatchs">
        INSERT INTO pl_eshop_stock_sync_config_limit_batch
        (`id`,
        `apply_id`,
        `profile_id`,
        `ktype_id`,
        `ptype_id`,
        `sku_id`,
        `batchno`,
        `batch_price`, `produce_date`, `expire_date`)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id},
            #{item.applyId},
            #{item.profileId},
            #{item.ktypeId},
            #{item.ptypeId},
            #{item.skuId},
            #{item.batchno},
            #{item.batchPrice},
            #{item.produceDate},
            #{item.expireDate}
            )
        </foreach>
    </insert>

    <update id="updateStockSyncConfigApplyLimitBatch">
        UPDATE
        `pl_eshop_stock_sync_config_limit_batch`
        <set>
            `apply_id` = #{applyId},
            `profile_id` = #{profileId},
            `ktype_id` = #{ktypeId},`ptype_id` = #{ptypeId},`sku_id` = #{skuId},
            `batchno` = #{batchno},`batch_price` = #{batch_price},`produce_date` = #{produceDate},
            `expire_date` = #{expireDate}
        </set>
        WHERE `id` = #{id} and profile_id=#{profileId}
    </update>
    <update id="updateAutoSyncStockEnabled">
        UPDATE
        `pl_eshop_stock_sync_default_rule`
        <set>
            `auto_sync_enabled` = #{autoSyncEnabled}
        </set>
        WHERE `id` = #{id}
        and profile_id= #{profileId}
    </update>
    <update id="batchDeleteCustomRule">
        update pl_eshop_stock_sync_rule
        set deleted=1
        where profile_id= #{profileId}
        and id in
        <foreach collection="ruleIdList" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </update>

    <delete id="deleteStockSyncConfigApplyLimitBatchs">
        delete
        from pl_eshop_stock_sync_config_limit_batch
        where profile_id = #{profileId}
        and apply_id in
        <foreach collection="applyIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </delete>



    <select id="getAllApplyStockConfigs" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.ApplyStockSyncConfig">
        select apply.id, apply.profile_id,apply.eshop_id,apply.platform_stock_code,apply.platform_sku_unique_id,apply.platform_xcode,apply.ptype_id,
        apply.sku_id,apply.unit_id,apply.sync_qty_target_type,apply.config_id,apply.zero_qty_sync_enabled,apply.auto_sync_enabled,
        apply.auto_sync_when_mapping_changed,apply.scene_type,apply.sync_time_type,apply.unique_id,
        batchInfo.id as 'batchInfo.id',batchInfo.apply_id as 'batchInfo.apply_id',
        batchInfo.profile_id,batchInfo.ktype_id,batchInfo.ptype_id,batchInfo.sku_id,batchInfo.batchno,
        batchInfo.batch_price,batchInfo.produce_date,batchInfo.expire_date,
        cfg.id as 'cfg.id', cfg.fullname as 'cfg.fullname',cfg.config_code as 'cfg.config_code',cfg.profile_id as 'cfg.profile_id',
        cfg.limit_ktype_ids as 'cfg.limit_ktype_ids',cfg.qty_type as 'cfg.qty_type',cfg.static_qty as 'cfg.static_qty', cfg.rule_cron as 'cfg.rule_cron',
        cfg.creator_id as 'cfg.creator_id',cfg.create_time as 'cfg.create_time',eshop.fullname as eshopName,
        p.fullname as ptypeName,concat(if(sku.propvalue_name1='','', concat(sku.propvalue_name1,';')),if(sku.propvalue_name2='','', concat(sku.propvalue_name2,';'))) as ptypePropName,
        xcode.xcode,pm.platform_fullname as platformFullname,psm.platform_properties_name as platformPropertiesName
        from pl_eshop_stock_sync_config_apply apply
        LEFT JOIN pl_eshop_stock_sync_config cfg on apply.profile_id  = cfg.profile_id and apply.config_id = cfg.id
        LEFT JOIN pl_eshop eshop on apply.profile_id  = eshop.profile_id and apply.eshop_id = eshop.otype_id and apply.eshop_id != 0
        LEFT JOIN pl_eshop_platform_store_mapping pepsm on apply.profile_id  = pepsm.profile_id and apply.eshop_id = pepsm.eshop_id
                    and  apply.platform_stock_code = pepsm.platform_store_stock_id and apply.platform_stock_code != ''

        LEFT JOIN pl_eshop_product_sku psm on psm.profile_id= apply.profile_id and psm.eshop_id=apply.eshop_id and
        psm.unique_id=apply.Platform_sku_unique_id and  apply.Platform_sku_unique_id != ''
        LEFT JOIN pl_eshop_product pm on psm.profile_id= pm.profile_id and psm.eshop_id=pm.eshop_id and psm.platform_num_id=pm.platform_num_id

        LEFT JOIN base_ptype_sku sku on apply.profile_id= sku.profile_id and apply.ptype_id=sku.ptype_id and apply.sku_id=sku.id  and apply.sku_id != 0
        LEFT JOIN base_ptype_xcode xcode on xcode.profile_id= sku.profile_id and xcode.ptype_id=sku.ptype_id and xcode.sku_id=sku.id and xcode.unit_id = apply.unit_id
        LEFT JOIN base_ptype p on p.profile_id= sku.profile_id and p.id=sku.ptype_id

        LEFT JOIN pl_eshop_stock_sync_config_limit_batch batchInfo on apply.profile_id  = batchInfo.profile_id and apply.id  = batchInfo.apply_id
        <where>
            apply.profile_id=#{profileId}
            <if test="configIds!=null and configIds.size()>0">
                and apply.config_id in
                <foreach collection="configIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="sceneType!=null">
                and apply.scene_type = #{sceneType}
            </if>
            <if test="otypeIds!=null and otypeIds.size()>0">
                and apply.eshop_id in
                <foreach collection="otypeIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="uniqueIds!=null and uniqueIds.size()>0">
                and apply.unique_id in
                <foreach collection="uniqueIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>

        </where>
    </select>

        <delete id="modifyAutoSyncState">
        update  pl_eshop_stock_sync_config_apply
            set auto_sync_enabled = #{autoSyncEnabled}
        where profile_id = #{profileId}
        and config_id  = #{configId}
    </delete>
    <delete id="deleteCustomRuleById">
        delete
        from pl_eshop_stock_sync_rule
        where profile_id = #{profileId}
        and id = #{id}
    </delete>
    <delete id="batchDeleteCustomRules">
        delete
        from pl_eshop_stock_sync_rule
        where profile_id = #{profileId}
        and id in
        <foreach collection="ids" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        />
    </delete>
    <delete id="deleteCustomRuleDetailByRuleId">
        delete
        from pl_eshop_stock_sync_rule_detail
        where profile_id = #{profileId}
        and rule_id = #{ruleId}
    </delete>
    <delete id="batchDeleteCustomRuleDetails">
        delete
        from pl_eshop_stock_sync_rule_detail
        where profile_id = #{profileId}
        and rule_id in
        <foreach collection="ruleIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        />
    </delete>


    <select id="getComboPtyeXcodeInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeXCodeEntity">
        select id,ptype_id,sku_id,unit_id,xcode
        from base_ptype_xcode
        where profile_id = #{profileId} and info_type=1 and defaulted=1
        and ptype_id in
        <foreach collection="ptypeIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>
    <select id="getSkuInfoByPtypeid" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeSkuEntity">
        select id,ptype_id
        from base_ptype_sku
        where profile_id = #{profileId} and stoped = 0 and deleted = 0
        and ptype_id in
        <foreach collection="ptypeIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>

    <select id="getProductSkuMultiStockSyncSetting"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.ProductSkuMultiStockSyncSettingEntity">
        select id, unique_id,profile_id, platfrom_sku_id, platform_num_id, platform_multi_type, platform_multi_Id,
               platform_multi_name, sync_type,sync_cron, fix_qty, sync_auto, eshop_id
        from pl_eshop_product_sku_multi_stock_sync_setting
        where profile_id=#{profileId}
        and unique_id in
        <foreach item="item" open="(" close=")" collection="uniqueIds" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryProductMarkByUniqueids"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select mark.*,bigdata.big_data as productMarkBigData from pl_eshop_product_mark mark
        left join pl_eshop_product_mark_data bigdata on bigdata.profile_id= mark.profile_id and bigdata.mark_id =
        mark.id
        where mark.profile_id=#{profileId}
        <if test="eshopId!=null">
            and mark.eshop_id = #{eshopId}
        </if>
        <if test="markCode!=null">
            and mark.mark_code = #{markCode}
        </if>
        <if test="eshopIds!=null and eshopIds.size()>0">
            and mark.eshop_id in
            <foreach collection="uniqueIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="uniqueIds!=null and uniqueIds.size()>0">
            and mark.unique_id in
            <foreach collection="uniqueIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="queryProductMarkByNumids"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select mark.*,bigdata.big_data as productMarkBigData from pl_eshop_product_mark mark
        left join pl_eshop_product_mark_data bigdata on bigdata.profile_id= mark.profile_id and bigdata.mark_id =
        mark.id
        where mark.profile_id=#{profileId}
        <if test="eshopId!=null">
            and mark.eshop_id = #{eshopId}
        </if>
        <if test="markCode!=null">
            and mark.mark_code = #{markCode}
        </if>
        <if test="eshopIds!=null and eshopIds.size()>0">
            and mark.eshop_id in
            <foreach collection="uniqueIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="numids!=null and numids.size()>0">
            and mark.platform_num_id in
            <foreach collection="numids" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="queryCustomRules" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select id,
        profile_id,
        rule_name,
        rule_type,
        state,
        rule_cron,
        target_type,
        IFNULL(zero_qty_sync_enabled, false) as zeroQtySyncEnabled,
        -- ifnull(auto_sync_enabled, false) autoSyncEnabled,
        ifnull(warehouse_stock_sync_enabled, false) warehouseStockSyncEnabled,
        ifnull(eshop_multi_stock_sync_enabled, false) eshopMultiStockSyncEnabled,
        ifnull(auto_up_shelf_enabled, false) autoUpShelfEnabled
        from `pl_eshop_stock_sync_rule`
        <if test="ktypeIds!=null and ktypeIds.size>0">
            JOIN (SELECT DISTINCT(rule_id) rule_id FROM pl_eshop_stock_sync_rule_detail
            WHERE profile_id=#{profileId}
            And ktype_id IN
            <foreach collection="ktypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            ) d ON d.rule_id=`pl_eshop_stock_sync_rule`.id
        </if>
        <where>
            profile_id=#{profileId}
            and deleted=0
            <!--            <if test="syncStockType>-1">-->
            <!--                and ifnull(auto_sync_enabled, 0)=#{syncStockType}-->
            <!--            </if>-->

            <if test="ruleName!=null and ruleName!=''">
                AND rule_name=#{ruleName}
            </if>
            <if test="filterRuleName!=null and filterRuleName!=''">
                AND rule_name like CONCAT('%',#{filterRuleName},'%')
            </if>
            <if test="ruleType!=null and ruleType!='' and ruleType>0">
                AND rule_type=#{ruleType}
            </if>
            <if test="ruleId!=null and ruleId>0">
                AND id=#{ruleId}
            </if>
        </where>
        order by rule_name
    </select>


    <insert id="batchinsertMultiStockSyncSettings">
        INSERT INTO pl_eshop_product_sku_multi_stock_sync_setting
        (`id`,
        `unique_id`,
        `profile_id`,
        `platfrom_sku_id`,
        `platform_num_id`,
        `platform_multi_type`,
        `platform_multi_Id`,
        `platform_multi_name`, `sync_type`, `sync_cron`,`fix_qty`,`sync_auto`,`eshop_id`
         )
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id},
            #{item.uniqueId},
            #{item.profileId},
            #{item.platfromSkuId},
            #{item.platformNumId},
            #{item.platformMultiType},
            #{item.platformMultiId},
            #{item.platformMultiName},
            #{item.syncType},
            #{item.syncCron},
            #{item.fixQty},
            #{item.syncAuto},
            #{item.eshopId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        `platfrom_sku_id` = VALUES(`platfrom_sku_id`),
        `platform_num_id` = VALUES(`platform_num_id`),
        `platform_multi_type` = VALUES(`platform_multi_type`),
        `platform_multi_Id` = VALUES(`platform_multi_Id`),
        `platform_multi_name` = VALUES(`platform_multi_name`),
        `sync_type` = VALUES(`sync_type`),
        `sync_cron` = VALUES(`sync_cron`),
        `fix_qty` = VALUES(`fix_qty`),
        `sync_auto` = VALUES(`sync_auto`),
        `eshop_id` = VALUES(`eshop_id`)
    </insert>
    <insert id="insertCustomRule">
        Replace
        into pl_eshop_stock_sync_rule
        (`id`,
        `profile_id`,
        `rule_name`,
        `rule_type`,
        `state`,
        `rule_cron`,
        `target_type`,
        `zero_qty_sync_enabled`,
        `warehouse_stock_sync_enabled`,
        `eshop_multi_stock_sync_enabled`,
        `auto_up_shelf_enabled`
        )
       VALUES (
        #{id},
        #{profileId},
        #{ruleName},
        #{ruleType},
        #{state},
        #{ruleCron},
        ifnull(#{targetType}, 99),
        ifnull(#{zeroQtySyncEnabled}, 0),
        ifnull(#{warehouseStockSyncEnabled},0),
        ifnull(#{eshopMultiStockSyncEnabled}, 0),
        ifnull(#{autoUpShelfEnabled}, 0)
        )
    </insert>
    <insert id="batchInsertCustomRuleDetail">
        Insert into pl_eshop_stock_sync_rule_detail
        (`id`,
        `rule_id`,
        `ktype_id`,
        `profile_id`,
        `create_time`,
        `update_time`
        )
        values
        <foreach collection="ruleDetailList" separator="," item="item">
            (#{item.id},
            #{item.ruleId},
            #{item.ktypeId},
            #{item.profileId},
            now(),
            now()
            )
        </foreach>
    </insert>


    <select id="getSyncRuleByRuleIds" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        SELECT
        rule.`id`,
        rule.`rule_name`,
        rule.`xcode`,
        rule.`rule_type`,
        rule.`rule_cron`,
        rule.`state`,
        rule.pcategory,
        rule.target_type,
        rule.warehouse_stock_sync_enabled,
        rule.eshop_multi_stock_sync_enabled,
        rule.auto_up_shelf_enabled,
        true as autoSyncEnabled
        FROM `pl_eshop_stock_sync_rule` rule
        WHERE rule.profile_id=#{profileId} and rule.deleted=0
        <if test="ruleIds!=null and ruleIds.size>0">
            And rule.id IN
            <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>

    <select id="getSyncRuleDetailByRuleIds" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockRuleDetail">
        SELECT
        rule.`id`,
        rule.`rule_id`,
        rule.`ktype_id`,
        rule.`frozen_qty`,
        rule.expire_date,
        rule.produce_date,
        rule.batch_price,
        rule.batchno,
        ktype.fullname
        FROM
        pl_eshop_stock_sync_rule_detail rule
        left join `base_ktype` ktype on ktype.id=rule.ktype_id and ktype.profile_id=rule.profile_id and ktype.deleted =
        0 and ktype.stoped = 0
        where rule.profile_id=#{profileId}
        <if test="ruleId!=null and ruleId>0">
            AND rule.rule_id=#{ruleId}
        </if>
        <if test="ruleIds!=null and ruleIds.size>0">
            And rule.rule_id IN
            <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>



    <select id="getMultiStockSyncDetailList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.MultiStockSyncDetail">
        SELECT
        `id`,`profile_id`, `rule_id`,`platform_multi_type`,`platform_multi_id`,`platform_multi_name`,`sync_type`,`sync_cron`,`fix_qty`
        FROM
        pl_eshop_multi_stock_sync_detail
        where profile_id=#{profileId}
        And rule_id IN
        <foreach collection="ruleIds" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
    </select>

    <select id="queryWarehousesSyncStoreByEshopId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping">
        select sp.id,sp.platform_store_stock_id,sp.platform_store_name,sp.ktype_id, sp.eshop_id ,sp.profile_id,sp.type, sp.platform_store_type,sp.type
        from pl_eshop_platform_store_mapping sp
        left join base_otype bo on sp.profile_id = bo.profile_id and sp.eshop_id=bo.id
        where sp.profile_id=#{profileId}
        and bo.deleted =0
        and bo.stoped=0
        and bo.ocategory in (0,1,2)
        and (sp.type=0 or (sp.type=1 and sp.platform_store_type=3))
        And sp.eshop_id IN
        <foreach collection="eshopIds" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
    </select>


</mapper>


